import { isEmpty } from 'lodash';
import { Asset, Task } from '../../types';
import { baseGraphQLApi, baseGraphQLApiThrowError } from './baseGraphQLApi';

const batchQuerySize = 1;
export class GQLApi {
  constructor(
    private gqlEndpoint: string,
    public token: string,
    private veritoneAppId: string
  ) {}

  async me() {
    const query = `query me {
      me {
        id
        name
        organizationId
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      me: {
        id: string;
        name: string;
        organizationId: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.me;
  }

  async getSDOSchemaId(dataRegistryId: string) {
    const query = `query fetchSchemaId {
      dataRegistry(id: "${dataRegistryId}") {
        id
        name
        publishedSchema {
          id
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      dataRegistry: {
        id: string;
        name: string;
        publishedSchema: {
          id: string;
        };
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.dataRegistry.publishedSchema.id;
  }

  async getDataRegistryId(schemaId: string) {
    const query = `query schema {
      schema(id: "${schemaId}") {
        dataRegistryId
      }
    }`;
    const resp = await baseGraphQLApiThrowError<{
      schema: {
        dataRegistryId: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.schema.dataRegistryId;
  }

  async getJob(id: string) {
    const query = `query job {
      job(id: "${id}") {
        target{
          id
          organizationId
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      job: {
        target: {
          id: string;
          organizationId: string;
        };
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.job;
  }

  async getApplicationJWT({
    appId,
    orgId,
    roleIds,
  }: {
    appId: string;
    orgId: string;
    roleIds: string[];
  }) {
    const query = `
        mutation ($appId:ID!, $orgId:ID, $roleIds: [ID]!){
            getApplicationJWT(input: {
                appId: $appId
                orgId: $orgId
                roleIds: $roleIds
            }) {
                token
            }
        }`;

    const resp = await baseGraphQLApiThrowError<{
      getApplicationJWT: {
        token: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { appId, orgId, roleIds },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.getApplicationJWT.token;
  }

  async getApplicationsByOffset(offset: number) {
    const query = `query applications {
      applications(offset: ${offset}, limit: 100) {
        records {
          id
          name
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      applications: {
        records: {
          id: string;
          name: string;
        }[];
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.applications.records;
  }

  async getApplications() {
    const results = [];
    let offset = 0;
    let count = 0;
    const limit = 100;
    do {
      const records = await this.getApplicationsByOffset(offset);
      offset += limit;
      count = records.length;
      if (count > 0) {
        results.push(...records);
      }
    } while (count === limit);
    return results;
  }

  async createTDOContentTemplate<T>({
    tdoId,
    schemaId,
    data,
  }: {
    tdoId: string;
    schemaId: string;
    data: T;
  }): Promise<string> {
    const query = `
      mutation updateTDO(
        $tdoId: ID!,
        $schemaId: ID!
        $data: JSONData!,
      ) {
        updateTDO(
           input: {
            id: $tdoId,
            contentTemplates: [{
              schemaId: $schemaId,
              data: $data,
            }]
          }
        ) {
          id
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<{
      updateTDO: { id: string };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {
        tdoId,
        schemaId,
        data,
      },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.updateTDO.id;
  }

  async getTDOContentTemplate<T>({
    tdoId,
    dataRegistryId,
  }: {
    tdoId: string;
    dataRegistryId: string;
  }): Promise<T | undefined> {
    const assets = await this.getTDOAssets({
      tdoId,
      assetTypes: ['content-template'],
      transform: 'JSON',
    });
    const contentTemplateAssets = [];
    const schemaRegistryMap = {} as Record<string, string>;
    for (const asset of assets) {
      if (!asset.sourceData?.schemaId) {
        continue;
      }
      let curDataRegistryId = schemaRegistryMap[asset.sourceData?.schemaId];
      if (!curDataRegistryId) {
        try {
          curDataRegistryId = await this.getDataRegistryId(
            asset.sourceData?.schemaId
          );
          schemaRegistryMap[asset.sourceData?.schemaId] = curDataRegistryId;
        } catch (error) {
          console.error(
            `Failed getting dataRegistryId for asset ${asset.id}, schemaId ${asset.sourceData?.schemaId}:`,
            error
          );
        }
      }
      if (curDataRegistryId === dataRegistryId) {
        contentTemplateAssets.push(asset);
      }
    }

    if (!isEmpty(contentTemplateAssets) && contentTemplateAssets[0].transform) {
      return JSON.parse(contentTemplateAssets[0].transform) as T;
    }
    return undefined;
  }

  async updateTDOContentTemplate<T>({
    tdoId,
    dataRegistryId,
    data,
  }: {
    tdoId: string;
    dataRegistryId: string;
    data: T;
  }) {
    const assets = await this.getTDOAssets({
      tdoId,
      assetTypes: ['content-template'],
    });
    const schemaId = await this.getSDOSchemaId(dataRegistryId);
    // create content template before deleting old ones.
    this.createTDOContentTemplate({
      tdoId,
      schemaId,
      data,
    });
    // delete old content template assets based on dataRegistryId
    const schemaRegistryMap = {} as Record<string, string>;
    const contentTemplateAssetIds = [];
    for (const asset of assets) {
      if (!asset.sourceData?.schemaId) {
        continue;
      }
      let curDataRegistryId = schemaRegistryMap[asset.sourceData?.schemaId];
      if (!curDataRegistryId) {
        try {
          curDataRegistryId = await this.getDataRegistryId(
            asset.sourceData?.schemaId
          );
          schemaRegistryMap[asset.sourceData?.schemaId] = curDataRegistryId;
        } catch (error) {
          console.error(
            `Failed getting dataRegistryId for asset ${asset.id}, schemaId ${asset.sourceData?.schemaId}:`,
            error
          );
        }
      }
      if (curDataRegistryId === dataRegistryId) {
        contentTemplateAssetIds.push(asset.id);
      }
    }
    await this.deleteAssets(contentTemplateAssetIds);
  }

  async getTDOAssets({
    tdoId,
    assetTypes,
    transform,
  }: {
    tdoId: string;
    assetTypes?: string[];
    transform?: 'XML2JSON' | 'Transcript2JSON' | 'JSON';
  }): Promise<Asset[]> {
    const results = [];
    let offset = 0;
    let count = 0;
    const limit = 100;
    do {
      const records = await this.getTDOAssetsByOffset({
        tdoId,
        assetTypes,
        transform,
        offset,
        limit,
      });
      offset += limit;
      count = records.length;
      if (count > 0) {
        results.push(...records);
      }
    } while (count === limit);
    return results;
  }

  async getTDOAssetsByOffset({
    tdoId,
    assetTypes,
    transform,
    offset,
    limit,
  }: {
    tdoId: string;
    assetTypes?: string[];
    transform?: 'XML2JSON' | 'Transcript2JSON' | 'JSON';
    offset: number;
    limit: number;
  }): Promise<Asset[]> {
    const query = `query tdo($tdoId: ID!, $assetTypes: [String!], $offset: Int, $limit: Int) {
      temporalDataObject(id: $tdoId) {
        assets(assetType: $assetTypes, offset: $offset, limit: $limit) {
          records {
            id
            ${transform ? `transform(transformFunction:${transform})` : ''}
            sourceData {
              schemaId
            }
          }
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      temporalDataObject: {
        assets: {
          records: Asset[];
        };
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoId, assetTypes, offset, limit },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp.temporalDataObject.assets.records;
  }

  async getTDOTasks(tdoId: string): Promise<Task[]> {
    const results = [];
    let offset = 0;
    let count = 0;
    const limit = 100;
    do {
      const records = await this.getTDOTasksByOffset({
        tdoId,
        offset,
        limit,
      });
      offset += limit;
      count = records.length;
      if (count > 0) {
        results.push(...records);
      }
    } while (count === limit);
    return results;
  }

  async getTDOTasksByOffset({
    tdoId,
    offset,
    limit,
  }: {
    tdoId: string;
    offset: number;
    limit: number;
  }): Promise<Task[]> {
    const query = `query tdo($tdoId: ID!, $offset: Int, $limit: Int) {
      temporalDataObject(id: $tdoId) {
        tasks(offset: $offset, limit: $limit){
          records{
            id
            status
            modifiedDateTime
            engine{
              id
              name
            }
          }
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      temporalDataObject: {
        tasks: {
          records: Task[];
        };
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoId, offset, limit },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp.temporalDataObject.tasks.records;
  }

  async deleteAsset(id: string) {
    const query = `
    mutation deleteAsset {
      deleteAsset(id: "${id}") {
        id
      }
    }`;
    const resp = await baseGraphQLApiThrowError<{
      deleteAsset: {
        id: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.deleteAsset.id;
  }

  async deleteAssets(ids: string[]) {
    const validData: { id: string }[] = [];
    const validError: Error[] = [];
    for (let i = 0; i < ids.length; i += batchQuerySize) {
      const batch = ids.slice(i, i + batchQuerySize);
      const deleteQuery = [] as string[];

      batch.forEach((id, index) => {
        deleteQuery.push(
          `deleteAsset_${id}_${index}:deleteAsset(id: "${id}") {
            id
          }`
        );
      });
      const query = `mutation { ${deleteQuery.join('\n')} }`;
      const resp = await baseGraphQLApi<
        Record<string, { id: string; message: string }>
      >({
        gqlEndpoint: this.gqlEndpoint,
        query,
        variables: {},
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });

      const result = Object.values(resp?.data || {});
      validData.push(...result.filter((res) => res?.id));
      if (resp.errors?.length) {
        validError.push(...resp.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    } else {
      return { data: validData };
    }
  }

  async getTDOByOffset({
    offset,
    limit,
    dateTimeFilter,
  }: {
    offset: number;
    limit: number;
    dateTimeFilter?: {
      field: string;
      fromDateTime: string;
    };
  }): Promise<string[]> {
    const query = `query temporalDataObjects($offset: Int, $limit: Int, $dateTimeFilter: [TemporalDataObjectDateTimeFilter!]) {
      temporalDataObjects(
        offset: $offset, 
        limit: $limit,
        dateTimeFilter: $dateTimeFilter
      ) {
        records {
          id
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      temporalDataObjects: {
        records: { id: string }[];
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {
        offset,
        limit,
        dateTimeFilter,
      },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.temporalDataObjects.records.map((tdo) => tdo.id);
  }
}
