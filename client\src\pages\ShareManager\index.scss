.share-manager {
  flex: 1;
  display: flex;
  overflow: hidden;
  margin-top: 10px;
  margin-bottom: 15px;
  flex-direction: column;

  &__header {
    gap: 15px;
    height: 52px;
    display: flex;
    margin-left: 21px;
    align-items: center;
    padding-bottom: 26px;

    .share-manager__title {
      gap: 10px;
      display: flex;
      font-size: 14px;
      align-items: center;
      color: var(--text-primary);
    }

    .share-manager__count {
      min-width: 38px;
      height: 26px;
      padding: 0 8px;
      display: flex;
      font-size: 12px;
      border-radius: 90px;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
      background: var(--chip-background-grey);
    }
  }

  &__content {
    flex: 1;
    gap: 15px;
    display: flex;
    overflow: hidden;
    flex-direction: row;
  }

  &__table {
    flex: 2;
    width: 100%;
  }

  &__details {
    margin-right: 15px;
    height: 100%;
  }
}
