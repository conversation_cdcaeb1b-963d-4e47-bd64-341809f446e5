import './index.scss';
import {
  AddToCase,
  DeleteWithLines,
  DeleteX,
  EditAttributes,
  ExpandMore,
  FolderMove,
  View,
} from '@assets/icons';
import { FolderOpenOutlined as FolderOpenOutlinedIcon } from '@mui/icons-material';
import Dialog from '@components/Dialog';
import FileCard from '@components/FileCard';
import EmptyState from '@components/Search/SearchContents/EmptyState';
import FileCardHeader from '@components/Search/SearchContents/FileCardHeader';
import Table, { Action, Column, DataMap } from '@components/Table';
import { useDataDetailsPanel } from '@hooks';
import { I18nTranslate } from '@i18n';
import {
  Box,
  Checkbox,
  IconButtonProps,
  TablePagination,
  Tooltip,
} from '@mui/material';
import type { FILES_SORT_FIELD, VFile } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import {
  deleteFile,
  searchCases,
  selectCases,
  selectFolderContentTemplateSchema,
} from '@store/modules/caseManager/slice';
import {
  editMetadata,
  toggleOpenEditDrawer,
} from '@store/modules/metadata/slice';
import {
  getFolders,
  ResultCategory,
  selectBlur,
  selectCategories,
  SelectedRow,
  selectSearchFiles,
  selectSearchView,
  selectSelectedResults,
  selectSort,
  selectViewType,
  setShowAddToCaseDialog,
  setSortBy,
  setSortDirection,
  toggleBlur,
  updateSelectedResults,
  selectFolderId,
  unfileFromCase,
} from '@store/modules/search/slice';
import { getThumbnailUrl } from '@utils/getThumbnails';
import { handleFileCardArrowKeys } from '@utils/helpers/handleFileCardArrowKeys';
import { SearchView, ViewType } from '@utils/local-storage';
import cn from 'classnames';
import dayjs from 'dayjs';
import { ChangeEvent, JSX, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { enqueueSnackbar } from 'notistack';
import { savePendingDeleteFileToLocalStorage } from '@utils/saveToLocalStorage';

export enum OrderBy {
  FILE_NAME = 'veritone-file.filename',
  CASE_ID = 'caseId',
  UPLOAD_DATE = 'createdTime',
  RETENTION_DATE = 'retentionDate',
  DESCRIPTION = 'description',
}

export interface SearchResult extends VFile {
  caseId?: string;
  retentionDate: string;
  fileIcon: JSX.Element;
  description: string;
}

const TABLE_MIN_WIDTH = '1075px';

// interface ResultsWithKey extends SearchResult {
//   key: string;
// }

const SearchTable = (props: Props) => {
  const {
    searchResults,
    pagination,
    loading,
    rowsPerPageOptions = [50, 100, 200, 300],
    showHeader = true,
    hasExpanseCol = false,
    classname = '',
    pendingDeleteIds = [],
    setPendingDeleteIds,
  } = props;

  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const blur = useSelector(selectBlur);
  const [page, setPage] = useState(
    Math.floor(pagination.from / pagination.limit) + 1
  );
  const [rowsPerPage, setRowsPerPage] = useState(
    pagination?.limit ?? rowsPerPageOptions[0]
  );

  const mapResultsToKey = (res: SearchResult[]) =>
    res.map((row) => ({
      ...row,
      key: `${Math.floor(Math.random() * 100000)}}-${row.id}`,
    }));

  const generateKey = () => Math.floor(Math.random() * 100000);

  const navigate = useNavigate();
  const { tdoId } = useParams();
  const [results, setResults] = useState(mapResultsToKey(searchResults));
  const [lastSelected, setLastSelected] = useState<number | null>(null);
  const [highlightedRow, setHighlightedRow] = useState<string>('');
  const [showDeleteFileDialog, setShowDeleteFileDialog] = useState(false);

  const viewType = useSelector(selectViewType);
  const selectedResults = useSelector(selectSelectedResults);
  const searchView = useSelector(selectSearchView);
  const searchFilesData = useSelector(selectSearchFiles);
  const categories = useSelector(selectCategories);
  const selectedCategories = categories.filter((c) => c.checked);
  const selectFolderContentTemplate = useSelector(
    selectFolderContentTemplateSchema
  );
  useEffect(() => {
    if (selectFolderContentTemplate.id) {
      dispatch(searchCases({}));
    }
  }, [dispatch, selectFolderContentTemplate]);
  const {
    data: { results: cases },
  } = useSelector(selectCases);
  const selectFolderIds = useMemo(
    () => cases.map((item) => item.folderId),
    [cases]
  );
  const selectedRowsSet = new Set(selectedResults.map(({ id }) => id));
  const filesSort = useSelector(selectSort);
  const parentTreeObjectIdsOnDataRegistry = useSelector(selectFolderId);
  const resultMap = useMemo(
    () =>
      results.reduce((acc: DataMap<SearchResult>, item, index) => {
        acc[item.id] = { index, item };
        return acc;
      }, {}),
    [results]
  );

  const prevFolderIdsRef = useRef<string[]>([]);

  const isCheckAll = useMemo(
    () =>
      !!results.length && results.every(({ id }) => selectedRowsSet.has(id)),
    [results, selectedRowsSet]
  );

  const hasSomeChecked = useMemo(
    () => results.some((row) => selectedRowsSet.has(row.id)),
    [results, selectedRowsSet]
  );

  const isEmptyResult = !results.length;

  const updateSelectedRows = (newSelectedRows: SelectedRow[]) => {
    dispatch(updateSelectedResults(newSelectedRows));
  };

  useEffect(() => {
    setResults(mapResultsToKey(searchResults));
    setPage(Math.floor(pagination.from / pagination.limit) + 1);
    setRowsPerPage(pagination.limit);
  }, [searchResults, loading]);

  const isValidOrderBy = (value: string): value is OrderBy =>
    Object.values(OrderBy).includes(value as OrderBy);

  const handleSort = (key: string) => {
    if (filesSort.type === key) {
      dispatch(setSortDirection(filesSort.order === 'asc' ? 'desc' : 'asc'));
    } else if (isValidOrderBy(key) && filesSort.type !== key) {
      dispatch(setSortBy(key as FILES_SORT_FIELD));
    }
  };

  const onPageChange = (
    _event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {
    props?.onPageChange?.(newPage, props?.category as ResultCategory);
    setLastSelected(null);
  };

  const onRowsPerPageChange = (event: ChangeEvent<HTMLInputElement>) => {
    const newRowsPerPage = Number(event.target.value);
    props.onPageSizeChange?.(newRowsPerPage, props?.category as ResultCategory);

    setLastSelected(null);
  };

  const handleCheckAll = () => {
    const rows = results.map(({ id, caseId }) => ({ id, caseId }));
    let newSelectedRows: SelectedRow[] = [];

    if (!isCheckAll) {
      newSelectedRows = selectedResults.concat(
        rows.filter(({ id }) => !selectedRowsSet.has(id))
      );
    } else {
      const resultSet = new Set(rows.map(({ id }) => id));
      newSelectedRows = selectedResults.filter(({ id }) => !resultSet.has(id));
    }

    updateSelectedRows(newSelectedRows);
    setLastSelected(null);
  };

  const handleSlice = (start: number, end: number) =>
    selectedResults.concat(
      results
        .slice(start, end + 1)
        .map(({ id, caseId }) => ({ id, caseId }))
        .filter(({ id }) => !selectedRowsSet.has(id))
    );

  const handleHoldShift = (startIndex: number, lastIndex: number) =>
    lastIndex > startIndex
      ? handleSlice(startIndex, lastIndex)
      : handleSlice(lastIndex, startIndex);

  const handleChangeCheckbox = (
    id: string,
    checked: boolean,
    e: ChangeEvent<HTMLInputElement>
  ) => {
    if (!resultMap[id]) {
      return;
    }

    const index = resultMap[id].index;
    const nativeEvent = e.nativeEvent;
    let newSelectedRows: SelectedRow[] = [];

    if (checked) {
      if (
        lastSelected !== null &&
        nativeEvent instanceof MouseEvent &&
        nativeEvent.shiftKey
      ) {
        newSelectedRows = handleHoldShift(index, lastSelected);
      } else {
        newSelectedRows = selectedResults.concat([
          { id, caseId: results[index].caseId },
        ]);
        setLastSelected(index);
      }
    } else {
      newSelectedRows = selectedResults.filter((row) => row.id !== id);
      setLastSelected(null);
    }

    updateSelectedRows(newSelectedRows);
  };

  const handleOpenEditMetadataDrawer = (rowId: string) => {
    if (viewType === ViewType.LIST) {
      setHighlightedRow(rowId);
    }
    dispatch(editMetadata(rowId));
    dispatch(toggleOpenEditDrawer(rowId));
  };

  const handleAddOrMoveToCaseDialog = (rowId: string) => {
    const row = results.find((row) => row.id === rowId);
    setHighlightedRow(rowId);
    dispatch(updateSelectedResults([{ id: rowId, caseId: row?.caseId }]));
    dispatch(setShowAddToCaseDialog(true));
  };

  useDataDetailsPanel({
    tdoId,
    navigationPath: '/search',
  });

  const handleDoubleClick = (tdoId?: string) => {
    navigate(`/search/data-details/${tdoId}`);
  };

  const getAddOrMoveCaseActionLabel = (rowId: string) => {
    const index = results.findIndex((row) => row.id === rowId);
    return results[index].caseId
      ? intl.formatMessage({ id: 'moveToAnotherCase' })
      : intl.formatMessage({ id: 'addToCase' });
  };
  const getAddOrMoveCaseIcon = (rowId: string) => {
    const index = results.findIndex((row) => row.id === rowId);
    return results[index].caseId ? <FolderMove /> : <AddToCase />;
  };

  const handleViewFile = (tdoId?: string) => {
    navigate(`/search/data-details/${tdoId}`);
  };
  useEffect(() => {
    if (
      selectFolderIds &&
      selectFolderIds.length > 0 &&
      JSON.stringify(selectFolderIds) !==
        JSON.stringify(prevFolderIdsRef.current)
    ) {
      dispatch(getFolders({ folderIds: selectFolderIds }));
      prevFolderIdsRef.current = selectFolderIds;
    }
  }, [dispatch, selectFolderIds]);

  const handleViewCase = (parentTreeObjectIds?: string[]) => {
    const folderId = parentTreeObjectIds?.[0] || '';
    const isValidFolderId =
      parentTreeObjectIdsOnDataRegistry.includes(folderId);
    if (folderId && isValidFolderId) {
      navigate(`/case-manager/${folderId}`);
    } else {
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'fileNotInCase',
          defaultMessage:
            'The file is not currently in a case. Please move this file to a case.',
        }),
        variant: 'warning',
      });
    }
  };

  const handleDelete = (tdoId: string) => {
    setHighlightedRow(tdoId);
    dispatch(updateSelectedResults([{ id: tdoId }]));
    setShowDeleteFileDialog(true);
  };

  const searchActions: Action[] = [
    {
      action: getAddOrMoveCaseActionLabel,
      icon: getAddOrMoveCaseIcon,
      onClick: handleAddOrMoveToCaseDialog,
    },
    {
      action: intl.formatMessage({ id: 'editMetadata' }),
      icon: <EditAttributes />,
      onClick: handleOpenEditMetadataDrawer,
    },
    {
      action: intl.formatMessage({ id: 'viewFile' }),
      icon: <View />,
      onClick: handleViewFile,
      isDivider: true,
    },
    {
      action: intl.formatMessage({ id: 'viewCase' }),
      icon: <FolderOpenOutlinedIcon />,
      onClick: (rowId: string) => {
        const row = searchResults.find((result) => result.id === rowId);
        handleViewCase(row?.parentTreeObjectIds);
      },
    },
    {
      action: intl.formatMessage({ id: 'delete' }),
      icon: <DeleteWithLines />,
      onClick: handleDelete,
    },
  ];

  const columns: Column<SearchResult>[] = [
    {
      header: searchResults.length ? (
        <Checkbox
          data-testid="check-box__check-all"
          indeterminate={!isCheckAll && hasSomeChecked}
          checked={isCheckAll}
          onChange={(_e) => handleCheckAll()}
        />
      ) : null,
      render: ({ rowId }) =>
        rowId && (
          <div className="search-table__checkbox">
            {hasExpanseCol && <ExpandMore visibility="hidden" />}
            <Checkbox
              data-testid={`check-box__check-row-${rowId}`}
              checked={selectedRowsSet.has(rowId)}
              onChange={(e, checked) => handleChangeCheckbox(rowId, checked, e)}
              onClick={(e) => e.stopPropagation()}
              onDoubleClick={(e) => e.stopPropagation()}
            />
          </div>
        ),
      width: '100px',
    },
    {
      header: intl.formatMessage({ id: 'fileName' }),
      field: 'fileName',
      render: ({ value, rowId }) => {
        const row = searchResults.find((row) => row.id === rowId);
        return (
          !Array.isArray(value) &&
          row && (
            <div className="search-table__file-name" data-testid="file-name">
              {row.fileIcon}
              <Tooltip title={value} placement="bottom-start">
                <div className="search-table__file-name-text">
                  <span>
                    {value || intl.formatMessage({ id: 'defaultEmpty' })}
                  </span>
                </div>
              </Tooltip>
            </div>
          )
        );
      },
      isSortable: true,
      width: '35%',
    },
    {
      header: intl.formatMessage({ id: 'caseId' }),
      field: 'caseId',
      render: ({ value }) =>
        !Array.isArray(value) && (
          <Tooltip
            title={value}
            placement="bottom-start"
            disableHoverListener={!value}
          >
            <div className="case-id">
              <span>{value || intl.formatMessage({ id: 'defaultEmpty' })}</span>
            </div>
          </Tooltip>
        ),
      isSortable: false,
      width: '16%',
    },
    {
      header: intl.formatMessage({ id: 'dateUploaded' }),
      field: 'createdTime',
      render: ({ value }) =>
        typeof value === 'string' && dayjs(value).format('MM/DD/YYYY'),
      isSortable: true,
      width: '16%',
    },
    // Leave this commented section here.  Retention Date will be part of future phase.
    // {
    //   header: intl.formatMessage({ id: 'retentionDate' }),
    //   field: 'retentionDate',
    //   render: ({ value }) =>
    //     typeof value === 'string' && dayjs(value).format('MM/DD/YYYY'),
    //   isSortable: true,
    //   width: '14%',
    // },
    {
      header: intl.formatMessage({ id: 'description' }),
      field: 'description',
      render: ({ value }) => (
        <div className="description">
          <span>{value ?? intl.formatMessage({ id: 'defaultEmpty' })}</span>
        </div>
      ),
      isSortable: false,
      width: '20%',
    },
  ];

  const handleCtrlRowClick = (id: string) => {
    if (!resultMap[id]) {
      return;
    }

    let newSelectedRows: SelectedRow[] = [];
    const index = resultMap[id].index;
    if (selectedRowsSet.has(id)) {
      newSelectedRows = selectedResults.filter((row) => row.id !== id);
      setLastSelected(null);
    } else {
      newSelectedRows = selectedResults.concat([
        { id, caseId: results[index].caseId },
      ]);
      setLastSelected(index);
    }
    updateSelectedRows(newSelectedRows);
  };

  const handleShiftRowClick = (id: string) => {
    if (!resultMap[id]) {
      return;
    }

    if (lastSelected !== null) {
      const index = resultMap[id].index;
      const newSelectedRows = handleHoldShift(index, lastSelected);
      updateSelectedRows(newSelectedRows);
    }
  };

  const handleKeyUp = (e: KeyboardEvent) => handleFileCardArrowKeys(e, results);

  useEffect(() => {
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [results]);

  const handleDeletionCancel = () => {
    setShowDeleteFileDialog(false);
  };

  const handleFileDeletionConfirm = () => {
    if (highlightedRow) {
      dispatch(unfileFromCase({ tdoId: highlightedRow }));
      dispatch(deleteFile({ tdoId: highlightedRow }));
      try {
        const deletedTdoIds = savePendingDeleteFileToLocalStorage([
          highlightedRow,
        ]);
        setPendingDeleteIds(deletedTdoIds.map((item) => item.value));
      } catch {
        console.error('unable to save deleted file to local storage');
      }
    }
    setShowDeleteFileDialog(false);
  };

  const handleCardCheck = (fileId: string) => {
    const file = results.find((row) => row.id === fileId);
    if (!file) {
      return;
    }
    let newSelectedRows: SelectedRow[] = [];
    if (selectedRowsSet.has(fileId)) {
      newSelectedRows = selectedResults.filter((row) => row.id !== fileId);
    } else {
      newSelectedRows = selectedResults.concat([
        { id: fileId, caseId: file.caseId },
      ]);
    }
    dispatch(updateSelectedResults(newSelectedRows));
  };

  return (
    <>
      {viewType === ViewType.LIST ? (
        <Table<SearchResult>
          data={results}
          dataMap={resultMap}
          columns={columns}
          row={{
            selected: highlightedRow,
            handleSelect: setHighlightedRow,
            handleDoubleClick: handleDoubleClick,
            handleCtrlRowClick: handleCtrlRowClick,
            handleShiftRowClick: handleShiftRowClick,
            pendingDeleteIds,
            pendingMoveFileIds: [],
          }}
          pagination={{
            page: page - 1,
            count: pagination.totalResults.value,
            onPageChange: onPageChange,
            onRowsPerPageChange: onRowsPerPageChange,
            rowsPerPage: pagination.limit,
            rowsPerPageOptions,
          }}
          sort={{
            direction: filesSort.order,
            handleSort: handleSort,
            orderBy: filesSort.type,
          }}
          styles={{
            emptyState: (
              <table
                className={cn({
                  'empty-table-grouped': searchView === SearchView.Grouped,
                  'empty-table-ungrouped': searchView === SearchView.UnGrouped,
                })}
              >
                <tbody className="empty-table">
                  <EmptyState
                    startExploring={!searchFilesData.searchParams}
                    selectACategory={selectedCategories.length === 0}
                  />
                </tbody>
              </table>
            ),
            isLoading: loading,
            classname: cn(`search-result__table`, classname, {
              'grouped-view':
                searchView === SearchView.Grouped && isEmptyResult,
              'ungrouped-view':
                searchView === SearchView.UnGrouped && isEmptyResult,
            }),
            noneCopyCell: true,
            isFixedTableLayout: true,
            fixedTableMinWidth: TABLE_MIN_WIDTH,
          }}
          actions={searchActions}
          extraProps={{}}
          showHeader={showHeader}
          data-testid="search-result-list-view"
        />
      ) : (
        <Box
          display="flex"
          flexDirection="column"
          className="search-result__card-view"
          data-testid="search-result-card-view"
        >
          {showHeader && (
            <FileCardHeader
              isCheckAll={isCheckAll}
              isIndeterminate={!isCheckAll && hasSomeChecked}
              isEmptyResult={isEmptyResult}
              onBlurSwitch={() => dispatch(toggleBlur())}
              blur={blur}
              searchView={searchView}
              onCheckAll={handleCheckAll}
            />
          )}
          <Box
            gap={3}
            flex={1}
            display="flex"
            flexWrap="wrap"
            justifyContent="center"
            className={cn({
              'search-result__card-view-content': !isEmptyResult,
              'empty-table-and-tile_grouped':
                isEmptyResult && searchView === SearchView.Grouped,
              'empty-table-and-tile-ungrouped':
                searchView === SearchView.UnGrouped && isEmptyResult,
            })}
          >
            {!isEmptyResult ? (
              <>
                {results.map((row, index) => {
                  const thumbnailUrl = getThumbnailUrl(
                    row.fileType,
                    row.programLiveImage || ''
                  );

                  return (
                    <FileCard
                      index={index}
                      fileId={row.id}
                      key={`file-card-${generateKey()}-${row.id}`}
                      fileName={row.fileName}
                      fileType={row.fileType}
                      dateUploaded={intl.formatDate(row.createdTime)}
                      caseId={row.caseId}
                      blurred={blur}
                      fileDuration={row.duration}
                      isChecked={selectedRowsSet.has(row.id)}
                      onCheck={handleCardCheck}
                      onMove={() => handleAddOrMoveToCaseDialog(row.id)}
                      onOpenEditMetadataDrawer={handleOpenEditMetadataDrawer}
                      onViewFile={() => handleViewFile(row.id)}
                      onViewCase={() => handleViewCase(row.parentTreeObjectIds)}
                      onDelete={() => handleDelete(row.id)}
                      thumbnailUrl={thumbnailUrl}
                      isDefaultThumbnail={thumbnailUrl === row.programLiveImage}
                      isPending={pendingDeleteIds.includes(row.id)}
                    />
                  );
                })}
              </>
            ) : (
              <table
                className={cn({
                  'empty-table-grouped': searchView === SearchView.Grouped,
                  'empty-table-ungrouped': searchView === SearchView.UnGrouped,
                })}
              >
                <tbody className="empty-table">
                  <EmptyState
                    startExploring={!searchFilesData.searchParams}
                    selectACategory={selectedCategories.length === 0}
                  />
                </tbody>
              </table>
            )}
          </Box>

          {!isEmptyResult && !loading && (
            <TablePagination
              component="div"
              data-testid="search-table-pagination-container"
              // sx={{ marginTop: 'auto' }}
              count={pagination.totalResults.value}
              page={page - 1}
              rowsPerPage={rowsPerPage}
              onPageChange={onPageChange}
              rowsPerPageOptions={rowsPerPageOptions}
              onRowsPerPageChange={onRowsPerPageChange}
              className="search-result__card-view-footer"
              labelRowsPerPage={intl.formatMessage({ id: 'rowPerPage' })}
              slotProps={{
                select: {
                  inputProps: {
                    'data-testid':
                      'search-table-pagination-rows-per-page-select',
                  },
                },
                actions: {
                  nextButton: {
                    'data-testid': 'search-table-pagination-next-button',
                  } as IconButtonProps,
                  previousButton: {
                    'data-testid': 'search-table-pagination-previous-button',
                  } as IconButtonProps,
                },
                displayedRows: {
                  'data-testid': 'search-table-pagination-displayed-rows',
                } as React.HTMLAttributes<HTMLParagraphElement>,
              }}
            />
          )}
        </Box>
      )}
      <Dialog
        open={showDeleteFileDialog}
        title={intl.formatMessage({ id: 'areYouSure' })}
        onClose={handleDeletionCancel}
        onConfirm={handleFileDeletionConfirm}
        confirmText={intl.formatMessage({ id: 'yesDeleteFile' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
        isDelete
        useTypeConfirmation
        disableConfirm={false}
      >
        <div>
          {I18nTranslate.TranslateMessage('deleteFilesConfirmationMsg')}
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('theFileWillBeDeleted')}
          </span>
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('everyOneInOrgWillLoseAccess')}
          </span>
        </div>
      </Dialog>
    </>
  );
};

interface Props {
  onPageChange?: (page: number, category?: ResultCategory) => void;
  onPageSizeChange?: (pageSize: number, category?: ResultCategory) => void;
  searchResults: SearchResult[];
  pagination: {
    from: number;
    to: number;
    limit: number;
    totalResults: {
      value: number;
      relation: string;
    };
  };
  loading: boolean;
  rowsPerPageOptions?: number[];
  showHeader?: boolean;
  hasExpanseCol?: boolean;
  classname?: string;
  searchView?: string;
  category?: string;
  pendingDeleteIds: string[];
  setPendingDeleteIds: (ids: string[]) => void;
}

export default SearchTable;
