import { Share } from './index';

const generateMockShares = (count: number): Share[] => {
  const mockShares: Share[] = [];
  for (let i = 1; i <= count; i++) {
    mockShares.push({
      id: `${i}`,
      shareName: `Case #${45662990 + i} Evidence Package`,
      sharedBy: i % 2 === 0 ? '<PERSON><PERSON>' : '<PERSON><PERSON>',
      fileCount: Math.floor(Math.random() * 20) + 1,
      recipientCount: Math.floor(Math.random() * 10) + 1,
      expirationDate: new Date(2025, 3, 20, 10, 0, 0).toISOString(),
      status: i % 3 === 0 ? 'EXPIRED' : 'ACTIVE',
    });
  }
  return mockShares;
};

export const mockShares = generateMockShares(25);
