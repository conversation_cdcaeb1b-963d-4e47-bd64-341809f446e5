.file-card {
  width: 240px;
  height: 320px;
  overflow: hidden;
  border-radius: 3px;
  background: var(--background-secondary);
  border: 1px solid var(--app-status-tab-border);
  cursor: pointer;

  &:focus-within,
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--focus-blue);
  }

  .file-card-image {
    width: 100%;
    height: 150px;
    overflow: hidden;
    position: relative;
    background: #212121;

    & > div > svg {
      width: 44px;
      height: 44px;
      color: #888888;
    }

    img {
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
  }

  .file-card-checkbox {
    top: 10px;
    left: 10px;
    position: absolute;
  }

  .blurred {
    filter: blur(5px);
    -webkit-filter: blur(5px);
    -moz-filter: blur(5px);
    -o-filter: blur(5px);
    -ms-filter: blur(5px);
  }

  .file-card-strip {
    height: 24px;
    padding: 0 10px;
    background: #def0ff;

    svg {
      color: #212121;
    }

    &-duration {
      color: #212121;
      font-size: 12px;
      font-weight: bold;
    }
  }

  .file-card-info {
    gap: 10px;
    display: flex;
    overflow: hidden;
    padding: 10px 15px;
    flex-direction: column;

    .info-title {
      width: 80px;
      font-size: 12px;
      color: var(--text-tertiary);
    }

    &-name {
      font-size: 14px;
      color: var(--text-primary);
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 200px;
      overflow: hidden;
      cursor: pointer;
      color: var(--text-link);
    }

    &-case-id {
      display: flex;
      font-size: 12px;
    }

    &-uploaded {
      font-size: 12px;
      color: var(--text-primary);
    }

    &-description {
      font-size: 14px;
      color: var(--text-primary);
    }
  }

  .file-card-menu {
    padding: 0 15px 10px 0;
  }
}

.disable-file-card {
  opacity: 0.5;
  pointer-events: none;
}
