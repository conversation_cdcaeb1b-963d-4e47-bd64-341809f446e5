import { I18nTranslate } from '@i18n';
import { PayloadAction } from '@reduxjs/toolkit';
import { ContentType, Metadata } from '@shared-types/metadata';
import { createAppSlice } from '@store/createAppSlice';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/index';
import { selectEvidenceTypeSchema } from '@store/modules/config/slice';
import { ApiStatus } from '@store/types';
import { getApiAuthToken } from '@store/utils';
import { GQLApi } from '@utils/helpers';
import { enqueueSnackbar } from 'notistack';
import { getFileMetadata, getFileMetadataByFolderId } from './getFileMetadata';
import { updateFileMetadata } from './updateFileMetadata';

export interface MetadataSliceState {
  openEditDrawer: boolean;
  metadata: {
    // byTdoId: Record<string, Metadata>;
    byFolderId: Record<
      string,
      {
        status: ApiStatus;
        error?: string;
        data: Record<string, Metadata>;
      }
    >;
  };

  tdoMetadata: {
    status: ApiStatus;
    error?: string;
    data: Omit<Metadata, 'contentType'> & { contentType: ContentType | '' };
  };
}

export const initialState: MetadataSliceState = {
  openEditDrawer: false,
  metadata: {
    // byTdoId: {},
    byFolderId: {},
  },

  tdoMetadata: {
    status: 'idle',
    data: {
      aiwareTdoId: '',
      description: '',
      uploadedDate: '',
      fileSize: 0,
      duration: 0,
      fileFormat: '',
      fileName: '',
      caseId: '',
      caseName: '',
      aiCognitionEngineOutput: [],
      sourceName: '',
      creator: '',
      sourceId: '',
      evidenceType: '',
      contentType: '',
      assetStatus: null,
      summary: { text: '' },
    },
  },
};

export const metadataSlice = createAppSlice({
  name: 'metadata',
  initialState,
  reducers: (create) => {
    const createThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>();

    return {
      toggleOpenEditDrawer: create.reducer(
        (state, _action: PayloadAction<string | undefined>) => {
          state.openEditDrawer = !state.openEditDrawer;
        }
      ),
      fetchMetadata: createThunk(
        async (
          {
            folderId,
            evidenceTypeSchemaId,
          }: { folderId: string; evidenceTypeSchemaId: string },
          thunkAPI
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const limit = 500;
          let offset = 0;
          const allMetadata = [];

          while (true) {
            const metadata = await getFileMetadataByFolderId(
              folderId,
              offset,
              limit,
              evidenceTypeSchemaId,
              gql
            );

            allMetadata.push(...metadata);
            if (metadata.length < limit) {
              break;
            }

            offset += limit;
          }

          return allMetadata;
        },
        {
          pending: (state, action) => {
            state.metadata.byFolderId[action.meta.arg.folderId] = {
              status: 'loading',
              error: undefined,
              data: {},
            };
          },
          fulfilled: (state, action) => {
            state.metadata.byFolderId[action.meta.arg.folderId] = {
              ...state.metadata.byFolderId[action.meta.arg.folderId],
              status: 'complete',
              error: undefined,
              data: action.payload.reduce(
                (acc, metadata) => ({
                  ...acc,
                  [metadata.aiwareTdoId]: metadata,
                }),
                {}
              ),
            };
          },
          rejected: (state, action) => {
            console.error('Could not fetch metadata.', action.error.message);
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('failedToRetrieveMetadata')
            );
            state.metadata.byFolderId[action.meta.arg.folderId] = {
              status: 'failure',
              error: action.error.message,
              data: {},
            };
          },
        }
      ),
      updateMetadata: createThunk(
        async (
          {
            tdoId,
            updatedMetadata,
            evidenceTypeSchemaId,
          }: {
            tdoId: string;
            updatedMetadata: Metadata;
            evidenceTypeSchemaId: string;
          },
          thunkAPI
        ) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const respo = await updateFileMetadata(
            tdoId,
            updatedMetadata,
            evidenceTypeSchemaId,
            gql
          );
          return respo;
        },
        {
          fulfilled: (state, action) => {
            state.tdoMetadata = initialState.tdoMetadata;
            for (const folderId of Object.keys(state.metadata.byFolderId)) {
              const folderData = state.metadata.byFolderId[folderId];
              if (folderData) {
                const tdoId = action.meta.arg.tdoId;
                if (folderData.data[tdoId]) {
                  folderData.data[tdoId] = {
                    ...folderData.data[tdoId],
                    ...action.payload,
                  };
                }
              }
            }
            enqueueSnackbar(I18nTranslate.TranslateMessage('metadataUpdated'));
          },
          rejected: (_state) => {
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('failedToUpdateMetadata')
            );
          },
        }
      ),
      editMetadata: createThunk(
        async (tdoId: string, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const evidenceTypeSchemaId = selectEvidenceTypeSchema(state).id;
          const metadata = await getFileMetadata(
            [tdoId],
            evidenceTypeSchemaId,
            gql
          );
          return metadata;
        },
        {
          pending: (state, _) => {
            state.tdoMetadata.data = initialState.tdoMetadata.data;
            state.tdoMetadata.status = 'loading';
            state.tdoMetadata.error = undefined;
          },
          fulfilled: (state, action) => {
            state.tdoMetadata.data = action.payload[0];
            state.tdoMetadata.status = 'complete';
            state.tdoMetadata.error = undefined;
          },
          rejected: (state, action) => {
            state.tdoMetadata.status = 'failure';
            state.tdoMetadata.error = `failed to get file metadata ${action.error.message}`;
            console.error('failed to get file metadata', action.error);
          },
        }
      ),
    };
  },
  selectors: {
    selectOpenMetadataDrawer: (state) => state.openEditDrawer,
    selectTdoMetadata: (state) => state.tdoMetadata,
    selectMetadataByFolderId: (state) => state.metadata.byFolderId,
  },
});

export const {
  toggleOpenEditDrawer,
  fetchMetadata,
  updateMetadata,
  editMetadata,
} = metadataSlice.actions;

export const {
  selectOpenMetadataDrawer,
  selectTdoMetadata,
  selectMetadataByFolderId,
} = metadataSlice.selectors;
