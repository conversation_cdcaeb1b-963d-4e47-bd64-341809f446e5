import z from 'zod';

const PENDING_FILE_KEY = 'investigate-pending-file';
const EXPIRATION_MS = 60 * 60 * 1000;
const VFileSchema = z.object({
  id: z.string(),
  fileName: z.string(),
  fileType: z.string(),
  parentTreeObjectIds: z.array(z.string()),
  createdByName: z.string(),
  createdTime: z.string(),
  updatedTime: z.string(),
  programLiveImage: z.string().optional(),
  caseId: z.string().optional(),
  description: z.string().optional(),
  duration: z.number(),
  evidenceType: z.string().optional(),
  sourceName: z.string().optional(),
});

const PendingLSFileSchema = z.object({
  value: VFileSchema,
  expiry: z.number(),
});

const PendingFilesByFolderIdSchema = z.record(
  z.string(),
  z.array(PendingLSFileSchema)
);
export type VFile = z.infer<typeof VFileSchema>;
export type PendingLSFile = z.infer<typeof PendingLSFileSchema>;
export type PendingFilesByFolderId = z.infer<
  typeof PendingFilesByFolderIdSchema
>;

export const getStoredPendingFiles = (): PendingFilesByFolderId => {
  try {
    const localStorageStr = localStorage.getItem(PENDING_FILE_KEY);
    if (!localStorageStr) {
      return {};
    }
    const parsedData: unknown = JSON.parse(localStorageStr);
    const validationResult = PendingFilesByFolderIdSchema.safeParse(parsedData);

    if (validationResult.success) {
      return validationResult.data;
    } else {
      console.error(
        'Invalid data structure in localStorage:',
        validationResult.error
      );
      return {};
    }
  } catch (error) {
    console.error('Failed to parse local storage data:', error);
    return {};
  }
};

const savePendingFiles = (data: PendingFilesByFolderId) => {
  localStorage.setItem(PENDING_FILE_KEY, JSON.stringify(data));
};

export const getPendingLSFiles = (folderId: string): PendingLSFile[] => {
  const now = Date.now();
  const parsedPendingFiles = getStoredPendingFiles();
  return (
    parsedPendingFiles[folderId]?.filter((item) => item.expiry > now) ?? []
  );
};

export const setPendingLSFile = ({
  folderId,
  id,
  fileName,
  fileType,
  createdTime,
}: {
  folderId: string;
  id: string;
  fileName: string;
  fileType: string;
  createdTime: string;
}) => {
  const now = Date.now();
  const parsedPendingFiles = getStoredPendingFiles();

  const validFiles = (parsedPendingFiles[folderId] ?? []).filter(
    (item) => item.expiry > now
  );

  const newPendingFile: PendingLSFile = {
    value: {
      id,
      fileName,
      fileType,
      parentTreeObjectIds: [],
      createdByName: '',
      createdTime,
      updatedTime: '',
      duration: -1,
    },
    expiry: now + EXPIRATION_MS,
  };

  savePendingFiles({
    ...parsedPendingFiles,
    [folderId]: [newPendingFile, ...validFiles],
  });
};

export const removePendingLSFiles = (ids: string[], folderId: string) => {
  const now = Date.now();
  const parsedPendingFiles = getStoredPendingFiles();

  if (!parsedPendingFiles[folderId]) {
    return;
  }

  const remainingFiles = parsedPendingFiles[folderId].filter(
    (item) => item.expiry > now && !ids.includes(item.value.id)
  );

  if (remainingFiles.length === 0) {
    // Remove empty folder entry
    delete parsedPendingFiles[folderId];
  } else {
    parsedPendingFiles[folderId] = remainingFiles;
  }

  savePendingFiles(parsedPendingFiles);
};
