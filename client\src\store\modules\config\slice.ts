import type { PayloadAction } from '@reduxjs/toolkit';
import { createAppSlice } from '@store/createAppSlice';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/index';
import { ApiStatus } from '@store/types';
import { getApiAuthToken } from '@store/utils';
import { GQLApi } from '@utils/helpers';
import { getSchemaId } from './getSchema';
import { enqueueSnackbar } from 'notistack';
import { I18nTranslate } from '@i18n';

export interface ConfigSliceState {
  statusSchema: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  tagSchema: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  evidenceTypeSchema: {
    status: ApiStatus;
    error?: string;
    id: string;
  };
  registryIds?: {
    caseRegistryId: string;
    evidenceTypeRegistryId: string;
    statusRegistryId: string;
    tagRegistryId: string;
  };
  featureFlags?: {
    shareManager: boolean;
  };
}

export const initialState: ConfigSliceState = {
  statusSchema: {
    status: 'idle',
    error: '',
    id: '',
  },
  tagSchema: {
    status: 'idle',
    error: '',
    id: '',
  },
  evidenceTypeSchema: {
    status: 'idle',
    error: '',
    id: '',
  },
  registryIds: {
    caseRegistryId: '',
    evidenceTypeRegistryId: '',
    statusRegistryId: '',
    tagRegistryId: '',
  },
};

export const configSlice = createAppSlice({
  name: 'appConfig',
  initialState,
  reducers: (create) => {
    const createThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>();
    return {
      onSetConfig: create.reducer(
        (state, action: PayloadAction<Window['config']>) =>
          Object.assign(state, action.payload)
      ),
      statusSchema: createThunk(
        async (_, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const dataRegistryId = config.registryIds.statusRegistryId;
          const id = await getSchemaId(dataRegistryId, gql);
          return id;
        },
        {
          pending: (state) => {
            state.statusSchema.status = 'loading';
            state.statusSchema.id = '';
          },
          fulfilled: (state, action) => {
            state.statusSchema.status = 'complete';
            state.statusSchema.id = action.payload;
          },
          rejected: (state, action) => {
            // TODO: add snack bar for failure
            state.statusSchema.status = 'failure';
            state.statusSchema.id = '';
            console.error('failed to get status schemaId', action.error);
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('failedGetStatusSchemaId'),
              { variant: 'error' }
            );
          },
        }
      ),
      tagSchema: createThunk(
        async (_, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const dataRegistryId = config.registryIds.tagRegistryId;
          const id = await getSchemaId(dataRegistryId, gql);
          return id;
        },
        {
          pending: (state) => {
            state.tagSchema.status = 'loading';
            state.tagSchema.id = '';
          },
          fulfilled: (state, action) => {
            state.tagSchema.status = 'complete';
            state.tagSchema.id = action.payload;
          },
          rejected: (state, action) => {
            // TODO: add snack bar for failure
            state.tagSchema.status = 'failure';
            state.tagSchema.id = '';
            console.error('failed to get tag schemaId', action.error);
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('failedGetTagSchemaId'),
              { variant: 'error' }
            );
          },
        }
      ),
      evidenceTypeSchema: createThunk(
        async (_, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const dataRegistryId = config.registryIds.evidenceTypeRegistryId;
          if (!dataRegistryId) {
            throw new Error('evidenceTypeRegistryId is missing');
          }

          const id = await getSchemaId(dataRegistryId, gql);
          return id;
        },
        {
          pending: (state) => {
            state.evidenceTypeSchema.status = 'loading';
            state.evidenceTypeSchema.id = '';
          },
          fulfilled: (state, action) => {
            state.evidenceTypeSchema.status = 'complete';
            state.evidenceTypeSchema.id = action.payload;
          },
          rejected: (state, action) => {
            state.evidenceTypeSchema.status = 'failure';
            state.evidenceTypeSchema.id = '';
            console.error('failed to get evidenceType schemaId', action.error);
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('failedGetEvidenceTypeSchemaId'),
              { variant: 'error' }
            );
          },
        }
      ),
    };
  },
  selectors: {
    selectConfig: (config) => config,
    selectStatusSchema: (state) => state.statusSchema,
    selectTagSchema: (state) => state.tagSchema,
    selectEvidenceTypeSchema: (state) => state.evidenceTypeSchema,
  },
});

export const { onSetConfig, statusSchema, tagSchema, evidenceTypeSchema } =
  configSlice.actions;

export const {
  selectConfig,
  selectStatusSchema,
  selectTagSchema,
  selectEvidenceTypeSchema,
} = configSlice.selectors;

export const { actions: configActions, reducer: configReducer } = configSlice;
