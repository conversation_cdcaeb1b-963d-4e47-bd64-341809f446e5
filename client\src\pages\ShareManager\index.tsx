import './index.scss';
import ShareTable from '@components/Share/ShareTable';
import { I18nTranslate } from '@i18n';
import { useCallback, useMemo, useState } from 'react';
import ShareDetails from './ShareDetails';
import { mockShareData } from './ShareDetails/mockData';

const ShareManager = () => {
  const [selected, setSelected] = useState('');
  const shareMap = useMemo(() => ({}), []);
  const totalResults = 0;

  const handleSelect = useCallback((id: string) => {
    setSelected(id);
  }, []);

  const handleDoubleClick = () => {
    // TODO: handle double click action
  };

  const handleTableRowContextMenu = () => {
    // TODO: handle context menu action
  };

  return (
    <div className="share-manager">
      <div className="share-manager__header">
        <div className="share-manager__title">
          {I18nTranslate.TranslateMessage('shareManager')}
        </div>
        <div className="share-manager__count">{totalResults}</div>
      </div>

      <div className="share-manager__content">
        <div className="share-manager__table">
          <ShareTable
            shareMap={shareMap}
            selected={selected}
            setSelected={setSelected}
            handleSelect={handleSelect}
            handleDoubleClick={handleDoubleClick}
            handleTableRowContextMenu={handleTableRowContextMenu}
          />
        </div>

        <div className="share-manager__details">
          <ShareDetails shareId={selected} data={mockShareData} />
        </div>
      </div>
    </div>
  );
};

export default ShareManager;
