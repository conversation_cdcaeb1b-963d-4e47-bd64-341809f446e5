import { describe, expect, it } from 'vitest';
import { pickStatus } from './updateFileStatus';

describe('pickStatus', () => {
  it('return processed status', () => {
    const tasks = [
      {
        id: '1',
        status: 'complete',
        modifiedDateTime: '2023-10-01T00:00:00Z',
        engine: {
          id: 'engine1',
          name: 'engine1',
        },
      },
      {
        id: '2',
        status: 'complete',
        modifiedDateTime: '2023-10-02T00:00:00Z',
        engine: {
          id: 'engine2',
          name: 'engine2',
        },
      },
    ];
    const got = pickStatus(tasks);
    expect(got).toBe('processed');
  });

  it('return failed status', () => {
    const tasks = [
      {
        id: '1',
        status: 'failed',
        modifiedDateTime: '2023-10-01T00:00:00Z',
        engine: {
          id: 'engine1',
          name: 'engine1',
        },
      },
      {
        id: '2',
        status: 'complete',
        modifiedDateTime: '2023-10-02T00:00:00Z',
        engine: {
          id: 'engine2',
          name: 'engine2',
        },
      },
    ];
    const got = pickStatus(tasks);
    expect(got).toBe('error');
  });

  it('return pending status', () => {
    const tasks = [
      {
        id: '1',
        status: 'running',
        modifiedDateTime: '2023-10-01T00:00:00Z',
        engine: {
          id: 'engine1',
          name: 'engine1',
        },
      },
      {
        id: '2',
        status: 'complete',
        modifiedDateTime: '2023-10-02T00:00:00Z',
        engine: {
          id: 'engine2',
          name: 'engine2',
        },
      },
    ];
    const got = pickStatus(tasks);
    expect(got).toBe('pending');
  });

  it('return most recent status', () => {
    const tasks = [
      // most recent is failed, should return error
      {
        id: '1',
        status: 'failed',
        modifiedDateTime: '2023-10-03T00:00:00Z',
        engine: {
          id: 'engine1',
          name: 'engine1',
        },
      },
      {
        id: '2',
        status: 'complete',
        modifiedDateTime: '2023-10-02T00:00:00Z',
        engine: {
          id: 'engine1',
          name: 'engine1',
        },
      },
    ];
    const got = pickStatus(tasks);
    expect(got).toBe('error');
  });
});
