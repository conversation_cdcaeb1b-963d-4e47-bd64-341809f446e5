.add-user-row {
  width: 100%;
  height: 46px;
  padding: 9px 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  border-radius: 2px;
  box-sizing: border-box;
  background-color: var(--background-secondary);

  &__input {
    width: 115px;
  }

  &__input--email {
    flex-grow: 1;
  }

  &__send-btn {
    width: 69px;
    height: 28px;
    font-size: 12px;
    border-color: #9e9e9e;
    color: var(--text-primary);
    background-color: var(--background-secondary);

    &:disabled,
    &.Mui-disabled {
      border: none;
      color: var(--text-disabled);
      background-color: var(--button-background-disabled);
    }
  }

  &__remove-btn .Sdk-MuiSvgIcon-root {
    width: 14px;
    height: 14px;
    color: var(--text-primary);
  }
}

#add-share-user
  .add-user-row__send-btn
  .Sdk-MuiInputBase-root.Sdk-MuiInputBase-root {
  input {
    height: 28px;
    padding: 6px 16px;
    border-color: #9e9e9e;
  }
}
