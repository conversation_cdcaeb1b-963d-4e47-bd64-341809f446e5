import { Request, Response } from 'express';
import { JobEvent } from '../types';
import { processJobEvent } from '../processJobEvent';

export async function jobEvent(req: Request, res: Response) {
  console.info('jobEvent - Received jobEvent', req.body);
  const jobEvent: JobEvent = req.body;
  if (!jobEvent || !jobEvent.jobId || jobEvent.vtn?.type !== 'job') {
    console.error('jobEvent - Invalid jobEvent data:', req.body);
    res.status(400).json({ message: 'Invalid jobEvent data', body: req.body });
    return;
  }
  try {
    await processJobEvent(
      jobEvent.jobId,
      req.app.locals.appConfig,
      req.app.locals.serviceToken
    );
    res.status(200).json({
      message: 'jobEvent processed successfully',
      jobId: jobEvent.jobId,
    });
    return;
  } catch (error) {
    console.error('jobEvent - Failed processing jobEvent', jobEvent, error);
    res.status(500).json({ message: 'Failed processing jobEvent' });
    return;
  }
}
