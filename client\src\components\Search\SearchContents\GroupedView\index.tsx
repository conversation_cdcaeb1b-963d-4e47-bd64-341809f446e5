import './index.scss';
import { ExpandMore, FaceRecognition, Filename } from '@assets/icons';
import CheckAllDialog from '@components/CheckAllDialog';
import { MappedSearchResult, mapSearchToResult } from '@components/Search';
import EmptyState from '@components/Search/SearchContents/EmptyState';
import FileCardHeader from '@components/Search/SearchContents/FileCardHeader';
import GroupedListHeader from '@components/Search/SearchContents/GroupedListHeader';
import SearchTable from '@components/Search/SearchContents/SearchTable';
import { useObserveScrollBar } from '@hooks';
import { I18nTranslate } from '@i18n';
import {
  ArrowDropDown as ArrowDropDownIcon,
  Code,
  TwoWheelerOutlined,
} from '@mui/icons-material';
import {
  Accordion,
  AccordionSummary,
  Checkbox,
  CircularProgress,
} from '@mui/material';
import { useAppDispatch } from '@store/hooks';
import {
  expandAllCategories,
  expandCategory,
  ResultCategory,
  SearchResults,
  selectBlur,
  selectCategories,
  SelectedRow,
  selectHideIfNoResults,
  selectSearchFiles,
  selectSelectedResults,
  selectViewType,
  toggleBlur,
  updateSelectedResults,
} from '@store/modules/search/slice';
import { ViewType } from '@utils/local-storage';
import cn from 'classnames';
import { JSX, useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { LicensePlateALPR } from '../../../../assets/icons/lib/engine-category/LicensePlateALPR';
import { ObjectDetection } from '../../../../assets/icons/lib/engine-category/ObjectDetection';
import { TextRecognition } from '../../../../assets/icons/lib/engine-category/TextRecognition';
import { Transcription } from '../../../../assets/icons/lib/engine-category/Transcription';

const CategoryIcons: Record<ResultCategory, JSX.Element> = {
  filename: <Filename />,
  transcription: <Transcription />,
  faceRecognition: <FaceRecognition />,
  objectDetection: <ObjectDetection />,
  vehicleRecognition: <TwoWheelerOutlined />,
  licensePlateRecognition: <LicensePlateALPR />,
  // sceneClassification: <ContentClassification />,
  textRecognition: <TextRecognition />,
  metadata: <Code />,
};

const GroupedView = ({
  groupedResults,
  onPageChange,
  onPageSizeChange,
  pendingDeleteIds,
  setPendingDeleteIds,
}: Props) => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const viewType = useSelector(selectViewType);
  const categories = useSelector(selectCategories);
  const hideIfNoResults = useSelector(selectHideIfNoResults);
  const selectedCategories = categories.filter((c) => c.checked);

  const filteredResults = selectedCategories.reduce(
    (acc: Partial<typeof groupedResults>, cat) => {
      if (groupedResults[cat.category]) {
        acc[cat.category] = groupedResults[cat.category];
      }
      return acc;
    },
    {}
  );
  const resultsArray = Object.values(filteredResults).reduce(
    (acc: MappedSearchResult[], result) =>
      acc.concat(mapSearchToResult(result?.data)),
    []
  );

  const searchFilesData = useSelector(selectSearchFiles);
  const selectedResults = useSelector(selectSelectedResults);
  const selectedRowsSet = new Set(selectedResults.map(({ id }) => id));

  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedCategory, setSelectedCategory] = useState<ResultCategory>();
  const blurred = useSelector(selectBlur);

  const { hasScrollBar, containerRef } = useObserveScrollBar();
  const isCheckAll = useMemo(
    () =>
      !!resultsArray.length &&
      resultsArray.every(({ id }) => selectedRowsSet.has(id)),
    [resultsArray, selectedRowsSet]
  );

  const hasSomeChecked = useMemo(
    () => resultsArray.some(({ id }) => selectedRowsSet.has(id)),
    [resultsArray, selectedRowsSet]
  );

  const isEmptyResult = useMemo(() => !resultsArray.length, [resultsArray]);

  const isCategoryCheckAll = useCallback(
    (category: ResultCategory) => {
      const results =
        filteredResults[category]?.data?.searchMedia.jsondata?.results ?? [];
      return (
        !!results.length &&
        results.every((result) =>
          selectedRowsSet.has(result.recording.recordingId)
        )
      );
    },
    [filteredResults, selectedRowsSet]
  );

  const hasSomeCategoryChecked = useCallback(
    (category: ResultCategory) => {
      const results =
        filteredResults[category]?.data?.searchMedia.jsondata?.results ?? [];
      return results.some((result) =>
        selectedRowsSet.has(result.recording.recordingId)
      );
    },
    [filteredResults, selectedRowsSet]
  );

  const isCategoryEmptyResult = useCallback(
    (category: ResultCategory) => {
      const results =
        filteredResults[category]?.data?.searchMedia.jsondata?.results ?? [];
      return !results.length;
    },
    [filteredResults]
  );

  const handleHeaderExpandAll = (isChecked: boolean) => {
    dispatch(expandAllCategories(isChecked));
  };

  const handleCheckAll = (isCheck: boolean, category?: ResultCategory) => {
    let newSelectedRows: SelectedRow[] = [];
    if (category) {
      const newRows = (
        filteredResults[category]?.data?.searchMedia.jsondata?.results ?? []
      ).map((result) => ({
        id: result.recording.recordingId,
        caseId: result.recording.caseId,
      }));

      if (isCheck) {
        newSelectedRows = selectedResults.concat(
          newRows.filter((row) => !selectedRowsSet.has(row.id))
        );
      } else {
        const idsToRemove = new Set(newRows.map((row) => row.id));
        newSelectedRows = selectedResults.filter(
          (row) => !idsToRemove.has(row.id)
        );
      }
    } else {
      if (isCheck) {
        newSelectedRows = selectedResults.concat(
          resultsArray.filter(({ id }) => !selectedRowsSet.has(id))
        );
      } else {
        const resultSet = new Set(resultsArray.map(({ id }) => id));
        newSelectedRows = selectedResults.filter(
          ({ id }) => !resultSet.has(id)
        );
      }
    }

    dispatch(updateSelectedResults(newSelectedRows));
  };

  const onOpenCategoryCheckAllMenu = (
    e: React.MouseEvent<HTMLElement>,
    category: ResultCategory
  ) => {
    e.stopPropagation();
    setMenuAnchorEl(e.currentTarget);
    setSelectedCategory(category);
  };

  const onCloseCategoryCheckAllMenu = () => {
    setMenuAnchorEl(null);
    setSelectedCategory(undefined);
  };

  const generateCategoryTable = (category: ResultCategory) => {
    const { data, status } = filteredResults[category] ?? {
      data: undefined,
      pagination: {},
      status: 'idle',
    };
    const pagination = {
      from: data?.searchMedia.jsondata?.from ?? 0,
      to: data?.searchMedia.jsondata?.to ?? 0,
      limit: data?.searchMedia.jsondata?.limit ?? 5,
      totalResults: {
        value: data?.searchMedia.jsondata?.totalResults?.value ?? 0,
        relation: data?.searchMedia.jsondata?.totalResults?.relation ?? '',
      },
    };

    return (
      <SearchTable
        searchResults={mapSearchToResult(data)}
        pagination={pagination}
        loading={status === 'loading'}
        rowsPerPageOptions={[10, 30, 50, 100]}
        showHeader={false}
        hasExpanseCol
        classname="grouped-view__list-view"
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        category={category}
        pendingDeleteIds={pendingDeleteIds}
        setPendingDeleteIds={setPendingDeleteIds}
      />
    );
  };

  const visibleCategories = hideIfNoResults
    ? selectedCategories.filter(({ category }) => {
        const searchResults = searchFilesData.groupedSearch?.[category];

        if (searchResults?.status === 'success') {
          return !!searchResults?.data?.searchMedia.jsondata.totalResults.value;
        }

        return true;
      })
    : selectedCategories;

  return (
    <div className="grouped-view" data-testid="grouped-view">
      {viewType === ViewType.LIST ? (
        <GroupedListHeader
          isCheckAll={isCheckAll}
          isIndeterminate={!isCheckAll && hasSomeChecked}
          isEmptyResult={isEmptyResult}
          onCheckAll={handleCheckAll}
          onExpandAll={handleHeaderExpandAll}
          onSetMenuAnchorEl={setMenuAnchorEl}
          hasScrollBar={hasScrollBar}
        />
      ) : (
        <FileCardHeader
          hasExpanseCol
          onBlurSwitch={() => dispatch(toggleBlur())}
          isCheckAll={isCheckAll}
          isIndeterminate={!isCheckAll && hasSomeChecked}
          isEmptyResult={isEmptyResult}
          onCheckAll={handleCheckAll}
          onExpandAll={handleHeaderExpandAll}
          onSetMenuAnchorEl={setMenuAnchorEl}
          blur={blurred}
        />
      )}

      <div className="accordion-container" ref={containerRef}>
        {visibleCategories.length > 0 ? (
          visibleCategories.map(({ id, category, color, expanded }) => (
            <Accordion key={id} expanded={expanded}>
              <AccordionSummary
                expandIcon={<ExpandMore />}
                aria-controls={category}
                id={id}
                style={{ backgroundColor: color }}
                onClick={() =>
                  dispatch(expandCategory({ category, expanded: !expanded }))
                }
              >
                <div
                  className="accordion-header__content-wrapper"
                  data-testid="accordion-header"
                >
                  <span
                    className={cn('accordion-header__checkbox-label', {
                      'empty-result-disabled': isCategoryEmptyResult(category),
                    })}
                    role="button"
                    aria-disabled={isCategoryEmptyResult(category)}
                    onClick={(e) => {
                      if (!isCategoryEmptyResult(category)) {
                        onOpenCategoryCheckAllMenu(e, category);
                      }
                    }}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      cursor: isCategoryEmptyResult(category)
                        ? 'not-allowed'
                        : 'pointer',
                    }}
                  >
                    <Checkbox
                      checked={isCategoryCheckAll(category)}
                      indeterminate={
                        !isCategoryCheckAll(category) &&
                        hasSomeCategoryChecked(category)
                      }
                      onClick={(e) => e.stopPropagation()}
                      onChange={(e) =>
                        handleCheckAll(e.target.checked, category)
                      }
                      data-testid={`accordion__check-box__check-all-${category}`}
                    />
                    <ArrowDropDownIcon />
                  </span>
                  <div className="accordion-header__category-label">
                    <div className="accordion-header__category-icon">
                      {CategoryIcons[category]}
                    </div>
                    <div
                      className="accordion-header__category-name"
                      data-testid={`accordion-name-${category}`}
                    >
                      {intl.formatMessage({ id: category })}
                    </div>
                    {!filteredResults?.[category] ||
                      (filteredResults[category]?.status === 'loading' && (
                        <CircularProgress size={20} />
                      ))}
                    {groupedResults[category]?.status &&
                      groupedResults[category]?.status !== 'loading' &&
                      groupedResults[category]?.status !== 'idle' && (
                        <span
                          data-testid={`accordion-header-result-count-${category}`}
                        >
                          {(groupedResults?.[category]?.data?.searchMedia
                            ?.jsondata?.totalResults.value ?? 0) >= 10000
                            ? intl.formatMessage({ id: '>' })
                            : ''}
                          {groupedResults[category]?.data?.searchMedia?.jsondata
                            ?.totalResults.value || 0}
                          {intl.formatMessage({ id: 'space' })}
                          {intl.formatMessage({ id: 'results' })}
                        </span>
                      )}
                  </div>
                </div>
              </AccordionSummary>
              {generateCategoryTable(category)}
            </Accordion>
          ))
        ) : (
          <div className="empty-state-container">
            <table>
              <EmptyState
                startExploring={!searchFilesData.searchParams}
                selectACategory={selectedCategories.length === 0}
              />
            </table>
          </div>
        )}
      </div>
      <CheckAllDialog
        menuAnchorEl={menuAnchorEl}
        onClose={onCloseCategoryCheckAllMenu}
        onCheckAll={handleCheckAll}
        category={selectedCategory}
      />
    </div>
  );
};

export interface Props {
  groupedResults: {
    [group in ResultCategory]?: SearchResults;
  };
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  pendingDeleteIds: string[];
  setPendingDeleteIds: (ids: string[]) => void;
}

export default GroupedView;
