import './index.scss';
import Dialog from '@components/Dialog';
import ShareStatus from '@components/Share/ShareStatus';
import Table, { Column, DataMap } from '@components/Table';
import { I18nTranslate } from '@i18n';
import { DeleteOutline as DeleteOutlineIcon } from '@mui/icons-material';
import { IconButton, Tooltip } from '@mui/material';
import dayjs from 'dayjs';
import { ChangeEvent, useMemo, useState } from 'react';
import { mockShares } from './mockData';

// mock type of Share
export interface Share {
  id: string;
  shareName: string;
  sharedBy: string;
  fileCount: number;
  recipientCount: number;
  expirationDate: string;
  status: 'ACTIVE' | 'EXPIRED';
}

interface Props<T> {
  shareMap: DataMap<Share>;
  selected: string;
  setSelected: (id: string) => void;
  handleSelect: (selectedId: string) => void;
  handleTableRowContextMenu?: (row: T) => void;
  handleDoubleClick: (rowId: string) => void;
  classname?: string;
}

const ShareTable = ({
  selected,
  handleSelect,
  handleTableRowContextMenu,
  handleDoubleClick,
  setSelected,
  classname,
}: Props<Share>) => {
  const intl = I18nTranslate.Intl();

  const [shares, setShares] = useState<Share[]>(mockShares);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [sortBy, setSortBy] = useState<keyof Share>('shareName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(50);
  // TODO: handle sorting and pagination when shares data updated
  const { sortedData, totalResults } = useMemo(() => {
    const sorted = [...shares].sort((a, b) => {
      const valA = a[sortBy];
      const valB = b[sortBy];

      if (typeof valA === 'string' && typeof valB === 'string') {
        return sortDirection === 'asc'
          ? valA.localeCompare(valB)
          : valB.localeCompare(valA);
      }
      if (typeof valA === 'number' && typeof valB === 'number') {
        return sortDirection === 'asc' ? valA - valB : valB - valA;
      }
      return 0;
    });
    return { sortedData: sorted, totalResults: sorted.length };
  }, [shares, sortBy, sortDirection]);
  const paginatedData = useMemo(
    () =>
      sortedData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage),
    [sortedData, page, rowsPerPage]
  );
  const shareMap: DataMap<Share> = useMemo(() => {
    const map: DataMap<Share> = {};
    paginatedData.forEach((item, index) => {
      map[item.id] = { index, item };
    });
    return map;
  }, [paginatedData]);

  // TODO: handle delete share
  const handleDelete = (rowId: string) => {
    setSelected(rowId);
    setOpenDeleteDialog(true);
  };

  const handleDeletionCancel = () => {
    setOpenDeleteDialog(false);
  };
  const handleDeletionConfirm = () => {
    setShares(shares.filter((share) => share.id !== selected));
    setOpenDeleteDialog(false);
  };
  const handleSort = (key: string) => {
    const isAsc = sortBy === key && sortDirection === 'asc';
    setSortDirection(isAsc ? 'desc' : 'asc');
    setSortBy(key as keyof Share);
  };
  const onPageChange = (
    _event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {
    setPage(newPage);
    setSelected('');
  };
  const onRowsPerPageChange = (event: ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    setSelected('');
  };

  const shareColumns: Column<Share>[] = [
    {
      field: 'shareName',
      header: intl.formatMessage({ id: 'shareName' }),
      render: ({ value }) => (
        <Tooltip title={value as string}>
          <div className="share-table-cell">
            <span>{value as string}</span>
          </div>
        </Tooltip>
      ),
      isSortable: true,
      width: '30%',
    },
    {
      field: 'sharedBy',
      header: intl.formatMessage({ id: 'sharedBy' }),
      isSortable: true,
      width: '15%',
    },
    {
      field: 'fileCount',
      header: intl.formatMessage({ id: 'fileCount' }),
      isSortable: true,
      width: '10%',
    },
    {
      field: 'recipientCount',
      header: intl.formatMessage({ id: 'recipientCount' }),
      isSortable: true,
      width: '10%',
    },
    {
      field: 'expirationDate',
      header: intl.formatMessage({ id: 'expDate' }),
      isSortable: true,
      width: '15%',
      render: ({ value }) =>
        value && typeof value === 'string'
          ? dayjs(value).format('MM/DD/YYYY')
          : '',
    },
    {
      field: 'status',
      header: '',
      width: '10%',
      render: ({ value }) => {
        const status = value as Share['status'];
        return <ShareStatus status={status} />;
      },
    },
    {
      header: '',
      width: '35px',
      render: ({ rowId }) => (
        <div className="menu-cell">
          <Tooltip title={intl.formatMessage({ id: 'delete' })}>
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                // TODO: handle delete share
                if (rowId) {
                  handleDelete(rowId);
                }
              }}
            >
              <DeleteOutlineIcon />
            </IconButton>
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <div className="share-table-container">
      <Table<Share>
        data={paginatedData}
        dataMap={shareMap}
        columns={shareColumns}
        row={{
          selected,
          handleSelect,
          handleTableRowContextMenu,
          handleDoubleClick,
        }}
        sort={{
          orderBy: sortBy,
          direction: sortDirection,
          handleSort,
        }}
        pagination={{
          page: page,
          count: totalResults,
          rowsPerPage: rowsPerPage,
          rowsPerPageOptions: [10, 25, 50, 100],
          onPageChange,
          onRowsPerPageChange,
        }}
        styles={{
          classname,
          isFixedTableLayout: true,
        }}
        extraProps={{}}
      />
      <Dialog
        open={openDeleteDialog}
        title={intl.formatMessage({ id: 'areYouSure' })}
        onClose={handleDeletionCancel}
        onConfirm={handleDeletionConfirm}
        confirmText={intl.formatMessage({ id: 'yesDeleteShare' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
        isDelete
        disableConfirm={false}
      >
        {I18nTranslate.TranslateMessage('deleteShareConfirmationMsg')}
      </Dialog>
    </div>
  );
};

export default ShareTable;
