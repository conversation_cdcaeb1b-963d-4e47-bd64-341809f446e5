import { updateFileStatusForOrg } from '../fileStatus/updateFileStatusForOrg';
import { AppConfig } from '../types';
import { GQLApi } from '../utils/gqlApi';

export const EXPIRATION_MIMUTES = 60;

export async function processScheduledEvent({
  token,
  appConfig,
  orgExpirationMap,
}: {
  token: string;
  appConfig: AppConfig;
  orgExpirationMap: Record<string, number | undefined>;
}) {
  const endpoint = `${appConfig.apiRoot}/${appConfig.graphQLEndpoint}`;
  const veritoneAppId = appConfig.veritoneAppId;
  const gqlApi = new GQLApi(endpoint, token, veritoneAppId);

  const me = await gqlApi.me();
  const organizationId = me.organizationId;
  if (!organizationId) {
    console.error(
      `scheduledEvent - processScheduledEvent - failed - no orgId:${organizationId} found`
    );
    throw new Error('No orgId not found.');
  }
  const expirationTime = orgExpirationMap[organizationId];
  const now = new Date();
  if (expirationTime && now.getTime() < expirationTime) {
    const expirationStr = new Date(expirationTime).toISOString();
    console.info(
      `scheduledEvent - processScheduledEvent - skip - orgId:${organizationId}, now: ${now.toISOString()}, expiration: ${expirationStr}`
    );
    return;
  }
  orgExpirationMap[organizationId] =
    now.getTime() + EXPIRATION_MIMUTES * 60 * 1000;
  console.info(
    `scheduledEvent -processScheduledEvent - processing - orgId:${organizationId}`,
    new Date().toISOString()
  );
  const start = new Date();
  try {
    await updateFileStatusForOrg({
      organizationId,
      dataRegistryId: appConfig.registryIds.fileStatusRegistryId,
      catchupHour: appConfig.catchupHour,
      gqlApi,
    });
    const end = new Date();
    const duration = (end.getTime() - start.getTime()) / 1000;

    console.info(
      `scheduledEvent -processScheduledEvent - completed - orgId:${organizationId}, duration:${duration} seconds`,
      end.toISOString()
    );
    return {
      organizationId,
      startTime: start.toISOString(),
      endTime: end.toISOString(),
      duration,
    };
  } catch (error) {
    const end = new Date();
    const duration = (end.getTime() - start.getTime()) / 1000;
    console.error(
      `scheduledEvent - processScheduledEvent - failed - orgId:${organizationId}, duration:${duration} seconds, error:`,
      error
    );
    throw error;
  } finally {
    // Clear the running expiration time after processing
    orgExpirationMap[organizationId] = undefined;
  }
}
