#!/bin/bash
. /getconfig-dynamicConfig.sh
echo "created env based config "
. /dynamicConfig-index-html.sh
echo "updated index.html"
cat /usr/share/nginx/html/index.html

shouldStartApi=$( jq -r .startApi /server/apiConfig.json )

if [ $shouldStartApi = "true" ]; then
  echo "Starting ngnix server...."
  nginx -g 'daemon on;';

  echo "Starting Node server...."
  exec node ./server/dist/server.js
else
  echo "Not Starting API"
  nginx -g 'daemon off;';
fi
