import { EvidenceTypeMetadata, FileMetadata } from '@shared-types/metadata';
import {
  BasicUserInfo,
  CaseSearchResults,
  EngineResult,
  FILES_SORT_DIRECTION,
  FILES_SORT_FIELD,
  Folder,
  Folders,
  RecordingMetadata,
  SearchMedia,
  SearchMediaCondition,
  TdoDescriptions,
  TemporalDataObject,
} from '@shared-types/types';
import { SearchMediaResponse } from '@store/modules/search/searchFiles';
import { FileSearchParams } from '@store/modules/search/slice';
import {
  baseGraphQLApi,
  baseGraphQLApiThrowError,
} from '@utils/helpers/gqlApi/baseGraphQLApi';
import { DateTime } from 'luxon';
import { validateStatusCondition } from '../validateStatusCondition';

const batchQuerySize = 10;
export enum SortBy {
  CaseId = 'caseId',
  CaseDate = 'caseDate',
  // RetentionDate = 'retentionDate',
}

// const INVESTIGATE_FILE_TAG = 'veritone_investigate';

export class GQLApi {
  constructor(
    private gqlEndpoint: string,
    public token: string,
    private veritoneAppId: string
  ) {}

  async me() {
    const query = `query me {
      me {
        id
        name
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      me: {
        id: string;
        name: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.me;
  }

  async getSDOSchemaId(dataRegistryId: string, abortSignal?: AbortSignal) {
    const query = `query fetchSchemaId {
      dataRegistry(id: "${dataRegistryId}") {
        id
        name
        publishedSchema {
          id
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      dataRegistry: {
        id: string;
        name: string;
        publishedSchema: {
          id: string;
        };
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
      abortSignal,
    });
    return resp.dataRegistry.publishedSchema.id;
  }

  async getAllSchemaIds(dataRegistryId: string, abortSignal?: AbortSignal) {
    const query = `query fetchAllSchemaIds {
      dataRegistry(id: "${dataRegistryId}") {
        schemas {
          records {
            id
          }
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      dataRegistry: {
        schemas: {
          records: Array<{ id: string }>;
        };
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
      abortSignal,
    });
    return resp.dataRegistry.schemas.records.map(({ id }) => id);
  }

  async createStructuredData<T>({
    schemaId,
    id,
    data,
  }: {
    schemaId: string;
    id?: string;
    data: T;
  }): Promise<{ id: string; data: T }> {
    const query = `
      mutation createStructuredData($schemaId: ID!, $id: ID, $data: JSONData) {
        createStructuredData(input: {schemaId: $schemaId, id: $id, data: $data }) {
          id
          data
        }
      }`;

    const variables = {
      schemaId,
      id,
      data,
    };

    const resp = await baseGraphQLApiThrowError<{
      createStructuredData: {
        id: string;
        data: T;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return {
      id: resp.createStructuredData.id,
      data: resp.createStructuredData.data,
    };
  }

  async createSDO<T>({
    schemaId,
    data,
  }: {
    schemaId: string;
    data: T;
  }): Promise<{ id: string; data: T }> {
    const resp = await this.createStructuredData<T>({ schemaId, data });
    return resp;
  }

  async getSDO<T>({ schemaId, id }: { schemaId: string; id: string }) {
    const query = `
    query getSDO {
      structuredDataObject(
        schemaId: "${schemaId}"
        id: "${id}"
      ) {
        schemaId
        id
        data
      }
    }`;
    const resp = await baseGraphQLApiThrowError<{
      structuredDataObject: {
        schemaId: string;
        id: string;
        data: T;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.structuredDataObject;
  }

  async updateSDO<T>({
    schemaId,
    id,
    data,
  }: {
    schemaId: string;
    id: string;
    data: T;
  }): Promise<{ id: string; data: T }> {
    const resp = await this.createStructuredData<T>({ schemaId, id, data });
    return resp;
  }

  async deleteSDO({ schemaId, id }: { schemaId: string; id: string }) {
    const query = `
    mutation deleteSDO {
      deleteStructuredData(input: {
        id: "${id}"
        schemaId: "${schemaId}"
      }) {
        id
      }
    }`;
    const resp = await baseGraphQLApiThrowError<{
      deleteStructuredData: {
        id: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.deleteStructuredData.id;
  }

  // dataArray is an array of objects in shape of {id, data}. If the id
  // is empty string, a new SDO will be created with data. If id is an sdo id, then
  // the sdo will updated with the data.
  async createSDOs(
    schemaId: string,
    dataArray: Array<{ id?: string; data: unknown }>
  ) {
    const validData: { id: string }[] = [];
    const validError: unknown[] = [];
    for (let i = 0; i < dataArray.length; i += batchQuerySize) {
      const batch = dataArray.slice(i, i + batchQuerySize);
      const inputStr: Array<string> = [];
      const updateQuery: Array<string> = [];
      const variables: {
        [key: string]: { id: string; schemaId: string; data: unknown };
      } = {};
      batch.forEach((item, index) => {
        inputStr.push(`$input${index}: CreateStructuredData!`);
        updateQuery.push(
          `createStructuredData_${index}:createStructuredData(input: $input${index}) {id}`
        );
        variables[`input${index}`] = {
          id: item.id ? item.id : '',
          schemaId,
          data: JSON.parse(JSON.stringify(item.data)),
        };
      });
      const query = `mutation createStructuredData(${inputStr.join(',')}){
        ${updateQuery.join('\n  ')}
      }`;

      const resp = await baseGraphQLApi<Record<string, { id: string }>>({
        gqlEndpoint: this.gqlEndpoint,
        query,
        variables,
        token: this.token,
        veritoneAppId: this.veritoneAppId,
      });
      const result = Object.values(resp?.data || {});
      validData.push(...result.filter((res) => res && res.id));
      if (resp.errors && resp.errors.length) {
        validError.push(...resp.errors);
      }
    }
    if (validError.length) {
      return { data: validData, errors: validError };
    }
    return { data: validData };
  }

  async getSDOs({
    schemaId,
    offset = 0,
    limit = 1000,
  }: {
    schemaId: string;
    offset?: number;
    limit?: number;
  }) {
    const query = `
    query getSDOs {
      structuredDataObjects(
        schemaId: "${schemaId}",
        offset: ${offset},
        limit: ${limit}
      ) {
        count
        records {
          id
          data
          createdDateTime
          modifiedDateTime
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      structuredDataObjects: {
        count: number;
        records: {
          id: string;
          data: unknown;
          createdDateTime: string;
          modifiedDateTime: string;
        }[];
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp;
  }

  async getLatestSDO(schemaId: string) {
    const query = `
    query getLatestSDO {
      structuredDataObjects(
        schemaId: "${schemaId}"
        limit:1
        offset:0
        orderBy: [{
          field: modifiedDateTime,
          direction: desc
        }]) {
        records {
          id
          data
        }
      }
    }`;
    const resp = await baseGraphQLApiThrowError<{
      structuredDataObjects: {
        records: [
          {
            id: string;
            data: unknown;
          },
        ];
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.structuredDataObjects.records?.[0];
  }

  async rootFolders(type: string) {
    const query = `query rootFolders {
      rootFolders(type: ${type}) {
        id
        name
        ownerId
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      rootFolders: {
        id: string;
        name: string;
        ownerId: string;
      }[];
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.rootFolders;
  }

  async getRootFolder() {
    const resp = await this.rootFolders('cms');
    const rootFolder = resp.find((folder) => folder.ownerId === null);
    return rootFolder;
  }

  async createRootFolders() {
    const query = `mutation {
      createRootFolders(rootFolderType: cms) {
        id
        name
        ownerId
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      rootFolders: {
        id: string;
        name: string;
        ownerId: string;
      }[];
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp;
  }

  async createFolder({
    name,
    description,
    userId,
    parentFolderId,
  }: {
    name: string;
    description?: string;
    userId: string;
    parentFolderId: string;
  }) {
    const query = `mutation createFolder(
      $name: String!
      $description: String!
      $userId: ID!
      $parentFolderId: ID!
    ) {
      createFolder(
        input: {
          name: $name
          description: $description
          userId: $userId
          parentId: $parentFolderId
        }
      ) {
        id
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      createFolder: {
        id: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { name, description, userId, parentFolderId },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.createFolder.id;
  }

  async deleteFolder(id: string) {
    const query = `
      mutation deleteFolder{
        deleteFolder(input: {id: "${id}", orderIndex: 0}) {
          id
        }
      }`;
    const resp = await baseGraphQLApiThrowError<{
      deleteFolder: {
        id: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.deleteFolder.id;
  }

  async createFolderContentTemplate({
    folderId,
    sdoId,
    schemaId,
  }: {
    folderId: string;
    sdoId: string;
    schemaId: string;
  }) {
    const query = `mutation createFolderContentTemplate(
      $folderId: ID!,
      $sdoId: ID!,
      $schemaId: ID!,
    ) {
      createFolderContentTemplate(input: {
        folderId: $folderId
        sdoId: $sdoId
        schemaId: $schemaId
      }) {
        id
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      createFolderContentTemplate: {
        id: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { folderId, sdoId, schemaId },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp.createFolderContentTemplate.id;
  }

  async searchCases({
    caseIdSearchText,
    tagIds,
    statusIds,
    limit,
    offset,
    sortBy = SortBy.CaseDate,
    sortDirection = 'desc',
    schemaId,
    caseStatuses,
  }: {
    caseIdSearchText?: string;
    tagIds?: string[];
    statusIds?: string[];
    limit: number;
    offset: number;
    sortBy?: SortBy;
    sortDirection?: 'desc' | 'asc';
    schemaId: string;
    caseStatuses?: string[];
  }) {
    const query = `query searchMedia($search: JSONData!) {
      searchMedia(search: $search) {
        jsondata
      }
    }`;

    const searchVars: {
      search: {
        index: string[];
        type: string;
        limit: number;
        offset: number;
        sort?: { field: string; order: string }[];
        query: {
          operator: string;
          conditions: {
            operator: string;
            field?: string;
            name?: string;
            value?: string;
            values?: string[];
            not?: boolean;
          }[];
        };
      };
    } = {
      search: {
        index: ['mine'],
        type: schemaId,
        limit,
        offset,
        sort: [
          ...(sortBy && sortDirection
            ? [{ field: sortBy, order: sortDirection }]
            : []),
          { field: 'createdDateTime', order: 'desc' },
        ],
        query: {
          operator: 'and',
          conditions: [],
        },
      },
    };

    const noneSoftDeletedCondition = {
      operator: 'exists',
      name: 'toBeDeletedTime',
      not: true,
    };

    // const noneDeletedCondition = { // TODO: Redundant -- to be removed
    //   operator: 'range',
    //   field: 'toBeDeletedTime',
    //   gt: '2024-01-01T00:00:00.000Z',
    //   not: true,
    // };

    const caseIdCondition = caseIdSearchText
      ? {
          field: 'caseId.fulltext',
          operator: 'query_string',
          value: `*${caseIdSearchText.trim()}*`,
        }
      : undefined;

    const { not, values } = validateStatusCondition(
      statusIds || [],
      caseStatuses
    );
    const statusCondition =
      statusIds && statusIds.length > 0
        ? {
            field: 'statusId',
            operator: 'terms',
            not,
            values,
          }
        : undefined;

    const tagCondition =
      tagIds && tagIds.length > 0
        ? {
            field: 'preconfiguredTagIds',
            operator: 'terms',
            values: tagIds.map((t) => t.trim()),
          }
        : undefined;

    searchVars.search.query.conditions.push(noneSoftDeletedCondition);
    // searchVars.search.query.conditions.push(noneDeletedCondition); // TODO: Redundant -- to be removed

    if (caseIdCondition) {
      searchVars.search.query.conditions.push(caseIdCondition);
    }
    if (tagCondition) {
      searchVars.search.query.conditions.push(tagCondition);
    }
    if (statusCondition) {
      searchVars.search.query.conditions.push(statusCondition);
    }

    if (!this.gqlEndpoint || !this.token || !this.veritoneAppId) {
      throw new Error('GQLApi not initialized');
    }

    const resp = await baseGraphQLApiThrowError<CaseSearchResults>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: searchVars,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp;
  }

  async searchMedia({
    fileName,
    folderId,
    dateFilter,
    sortBy,
    sortDirection,
    limit,
    offset,
  }: {
    fileName?: string;
    folderId?: string;
    dateFilter?: {
      startDate: string;
      endDate: string;
    };
    sortBy: FILES_SORT_FIELD;
    sortDirection: FILES_SORT_DIRECTION;
    offset: number;
    limit?: number;
  }) {
    const query = `
      query searchMedia($search: JSONData!) {
        searchMedia(search: $search) {
          jsondata
        }
      }
    `;

    const conditions: SearchMediaCondition[] = [
      // {
      //   operator: 'query_object',
      //   field: 'tags',
      //   not: false,
      //   query: {
      //     operator: 'term',
      //     field: 'tags.value',
      //     value: INVESTIGATE_FILE_TAG,
      //     dotNotation: true,
      //   },
      // },
    ];

    // Filter by file name
    if (fileName) {
      conditions.push({
        operator: 'and',
        conditions: fileName.split(' ').map((value) => ({
          field: 'veritone-file.filename',
          operator: 'query_string',
          value: `*${value.trim()}*`,
        })),
      });
    }

    // Filter by case
    if (folderId) {
      const folder = await this.getFolder({ folderId });

      conditions.push({
        field: 'parentTreeObjectIds',
        operator: 'terms',
        values: [folder.treeObjectId],
      });
    }

    // Filter by date range
    if (dateFilter) {
      const startDate = new Date(dateFilter.startDate);
      const endDate = new Date(dateFilter.endDate);
      endDate.setUTCHours(23, 59, 59, 999); // end of the day

      conditions.push({
        operator: 'range',
        field: 'absoluteStartTimeMs',
        gte: startDate.toISOString(),
        lte: endDate.toISOString(),
      });
    }

    conditions.push({
      operator: 'query_object',
      field: 'tags',
      not: true,
      query: {
        operator: 'query_string',
        field: 'tags.key',
        value: 'toBeDeletedTime',
        dotNotation: true,
      },
    });

    const searchQuery = {
      index: ['mine'],
      type: 'file',
      select: ['veritone-file'],
      limit,
      offset,
      sort: [{ field: sortBy, order: sortDirection }],
      query:
        conditions.length > 0 ? { operator: 'and', conditions } : undefined,
    };

    const resp = await baseGraphQLApiThrowError<
      SearchMedia<{
        recording: {
          recordingId: string;
          createdTime: string;
          modifiedTime: string;
          parentTreeObjectIds: string[];
          programLiveImage?: string;
          creator: string;
        };
        context: {
          'veritone-file': {
            createdbyname: string;
            filename: string;
            filetype?: string;
            mimetype: string;
            size: number;
            duration?: number;
          };
        }[];
      }>
    >({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { search: searchQuery },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp;
  }

  async getFolder({ folderId }: { folderId: string }) {
    const query = `
      query getFolder($folderId: ID!) {
        folder(id: $folderId) {
          id
          name
          treeObjectId
          createdDateTime
          modifiedDateTime
          contentTemplates {
            id
            sdo {
              id
              data
              schemaId
            }
          }
        }
      }`;

    const resp = await baseGraphQLApiThrowError<Folder>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { folderId },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp.folder;
  }

  async getMultiFolders(folderIds: string[]) {
    const query = `
      {
        ${folderIds
          .map(
            (id, index) => `
        folder_${index}: folder(id: "${id}") {
          id
          name
          treeObjectId
        }`
          )
          .join('\n')}
      }
    `;

    const resp = await baseGraphQLApi<{
      [key: string]: Folder['folder'];
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    const folders: Folder['folder'][] = [];
    const treeObjectIds: string[] = [];

    for (const [key, folder] of Object.entries(resp.data)) {
      if (key.includes('folder_') && folder !== null) {
        folders.push(folder);
        treeObjectIds.push(folder.treeObjectId);
      }
    }

    return { folders, treeObjectIds };
  }

  async getFolders(folderIds: string[]) {
    const query = `
      query getFolders {
        ${folderIds
          .map(
            (folderId, index) => `
            folder_${index}: folder(id: "${folderId}") {
              id
              name
              createdDateTime
              treeObjectId
            }
          `
          )
          .join('\n')}
      }
    `;

    const resp = await baseGraphQLApi<Folders>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp;
  }

  async getTdoDetails({ tdoId }: { tdoId: string }) {
    const query = `
      query tdo($tdoId: ID!){
        temporalDataObject(id: $tdoId) {
          id
          createdDateTime
          details
          folders {
              id
              name
            }
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<{
      temporalDataObject: TemporalDataObject;
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoId },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp.temporalDataObject;
  }

  async getTdoDescriptions(tdoIds: string[]) {
    const query = `
      query getTdoDescriptions($tdoIds: [ID]!) {
        temporalDataObjects(ids: $tdoIds) {
          offset
          limit
          count
          records {
            id
            description
          }
        }
      }
    `;

    return await baseGraphQLApi<TdoDescriptions>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoIds },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
  }

  async softDeleteFile({
    tdoId,
    details,
  }: {
    tdoId: string;
    details: TemporalDataObject['details'];
  }) {
    const query = `
    mutation softDeleteTdo($tdoId: ID!, $details: JSONData!) {
      updateTDO(input: {
        id: $tdoId
        details: $details
      }) {
        id
      }
    }`;

    const variables = {
      tdoId: tdoId,
      details: {
        ...details,
        tags: [
          ...(details?.tags ?? []),
          { key: 'toBeDeletedTime', value: DateTime.now().toUTC().toISO() },
        ],
      },
    };

    return await baseGraphQLApiThrowError<Folder>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
  }

  async getBasicUserInfo({ id }: { id: string }) {
    const query = `query {
      basicUserInfo(id: "${id}") {
        id
        firstName
        lastName
        email
      }
    }`;

    const resp = await baseGraphQLApiThrowError<BasicUserInfo>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp.basicUserInfo;
  }
  async getUsersInfo({ userIds }: { userIds: string[] }) {
    const query = `
    query fetchUsers($userIds: [ID]) {
      users(ids: $userIds) {
        records{
         lastName
         firstName
         email
         id
        }  
      }
      }
    `;
    const resp = await baseGraphQLApi<{
      users: {
        records: [
          {
            lastName: string;
            firstName: string;
            email: string;
            id: string;
          },
        ];
      };
      errors?: {
        name?: string;
        data?: {
          parameters?: string[];
        };
      }[];
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { userIds },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });
    return resp;
  }
  async moveFile({
    fileId,
    oldFolderId,
    newFolderId,
  }: {
    fileId: string;
    oldFolderId: string;
    newFolderId: string;
  }) {
    const query = `
      mutation moveTdo($tdoId: ID!, $oldFolderId: ID!, $newFolderId: ID!) {
        moveTemporalDataObject(input: {
          tdoId: $tdoId
          oldFolderId: $oldFolderId
          newFolderId: $newFolderId
        }) {
          id
        }
      }`;

    const resp = await baseGraphQLApiThrowError<{
      moveTemporalDataObject: {
        id: string;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoId: fileId, oldFolderId, newFolderId },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp;
  }

  async fileTemporalDataObject({
    folderId,
    tdoId,
  }: {
    folderId: string;
    tdoId: string;
  }) {
    const query = `
      mutation fileTemporalDataObject($tdoId: ID!, $folderId: ID!) {
        fileTemporalDataObject(input: {tdoId: $tdoId, folderId: $folderId}) {
          id
          folders {
            id
          }
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<{
      fileTemporalDataObject: {
        id: string;
        folders: { id: string }[];
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoId, folderId },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp;
  }

  async unfileTemporalDataObject({
    folderId,
    tdoId,
  }: {
    folderId: string;
    tdoId: string;
  }) {
    const query = `
      mutation unfileTemporalDataObject($tdoId: ID!, $folderId: ID!) {
        unfileTemporalDataObject(input: {tdoId: $tdoId, folderId: $folderId}) {
          id
          folders {
            id
          }
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<{
      unfileTemporalDataObject: {
        id: string;
        folders: { id: string }[];
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoId, folderId },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp;
  }

  async getTDOFolder({ tdoId }: { tdoId: string }) {
    const query = `
      query getTDOFolder($tdoId: ID!) {
        temporalDataObject(id: $tdoId) {
          id
          folders {
            id
            name
          }
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<{
      temporalDataObject: {
        id: string;
        folders: Partial<Folder['folder']>[];
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoId },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp.temporalDataObject.folders;
  }

  async searchFiles({
    searchVars,
    queryName,
    abortSignal,
  }: {
    searchVars: Record<string, unknown>;
    queryName: string;
    abortSignal?: AbortSignal;
  }) {
    const query = `
      query ${queryName}($search: JSONData!) {
        searchMedia(search: $search) {
          jsondata
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<SearchMediaResponse>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: searchVars,
      token: this.token,
      veritoneAppId: this.veritoneAppId,
      abortSignal,
    });

    return resp;
  }

  async searchCaseByIds({
    caseIds,
    caseSchemaId,
    abortSignal,
  }: {
    caseIds: string[];
    caseSchemaId: string;
    abortSignal?: AbortSignal;
  }) {
    const query = `
      query searchCaseIds($search: JSONData!) {
        searchMedia(search: $search) {
          jsondata
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<
      SearchMedia<{
        sdoId: string;
        caseId: string;
        caseDate: string;
        caseName: string;
        folderId: string;
        statusId: string;
        createdBy: string;
        modifiedBy: string;
        description: string;
        createdDateTime: string;
        toBeDeletedTime: string;
        modifiedDateTime: string;
        preconfiguredTagIds: string[];
        id: string;
      }>
    >({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {
        search: {
          index: ['mine'],
          type: caseSchemaId,
          query: {
            operator: 'or',
            conditions: caseIds.map((caseId) => ({
              field: 'caseId',
              operator: 'query_string',
              value: caseId,
            })),
          },
        },
      },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
      abortSignal,
    });

    return resp;
  }

  async searchCaseByCondition({
    caseStatusesContidion,
    caseTagsCondition,
    caseSchemaId,
    abortSignal,
  }: {
    caseStatusesContidion?: FileSearchParams['caseStatusesFilter'];
    caseTagsCondition?: FileSearchParams['caseTagsFilter'];
    caseSchemaId: string;
    abortSignal?: AbortSignal;
  }) {
    const query = `
      query searchCaseByStatusIds($search: JSONData!) {
        searchMedia(search: $search) {
          jsondata
        }
      }
    `;

    const conditions: (
      | FileSearchParams['caseStatusesFilter']
      | FileSearchParams['caseTagsFilter']
    )[] = [];

    if (caseStatusesContidion) {
      conditions.push(caseStatusesContidion);
    }

    if (caseTagsCondition) {
      conditions.push(caseTagsCondition);
    }

    const resp = await baseGraphQLApiThrowError<
      SearchMedia<{
        sdoId: string;
        caseId: string;
        caseDate: string;
        caseName: string;
        folderId: string;
        statusId: string;
        createdBy: string;
        modifiedBy: string;
        description: string;
        createdDateTime: string;
        toBeDeletedTime: string;
        modifiedDateTime: string;
        preconfiguredTagIds: string[];
        id: string;
      }>
    >({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {
        search: {
          offset: 0,
          limit: 10000,
          index: ['mine'],
          type: caseSchemaId,
          query: {
            operator: 'or',
            conditions,
          },
        },
      },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
      abortSignal,
    });

    return resp;
  }

  async getSchemaProperties(dataRegistryId: string, abortSignal?: AbortSignal) {
    const query = `
      {
        schemaProperties(
          dataRegistryVersion:{
            id: "${dataRegistryId}"
          }
        ) {
          records {
            type
            path
            searchPath
            title
            schema {
              id
              dataRegistry {
                name
                organization {
                  name
                }
              }
              majorVersion
            }
          }
          limit
          offset
          count
        }
      }
  `;

    const resp = await baseGraphQLApiThrowError<{
      schemaProperties: {
        records: {
          type: string;
          path: string;
          searchPath: string;
        }[];
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
      abortSignal,
    });

    return resp;
  }

  async getLibraries(abortSignal?: AbortSignal) {
    const query = `
      query getLibraries{
        libraries(limit: 200) {
          records {
            id
          }
        }
      }`;

    const resp = await baseGraphQLApiThrowError<{
      libraries: {
        records: {
          id: string;
          libraryId: string;
          name: string;
          version: number;
          coverImageUrl: null | string;
          organizationId: string;
          libraryType: {
            id: string;
            label: string;
            entityIdentifierTypes: {
              id: string;
            }[];
          };
          summary: {
            entityCount: number;
            unpublishedEntityCount: number;
          };
          createdDateTime: string;
          engineModels: {
            records: {
              id: string;
              trainStatus: string;
              libraryVersion: number;
              engineId: string;
            }[];
          };
        }[];
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {},
      token: this.token,
      veritoneAppId: this.veritoneAppId,
      abortSignal,
    });

    return resp;
  }

  async getFileMetadata(tdoIds: string[]) {
    const query = `
      query fetchFileMetadata($tdoIds: [ID]!) {
        temporalDataObjects(ids: $tdoIds) {
          offset
          limit
          count
          records {
            id
            createdDateTime
            description
            details
            name
            createdBy
            folders {
              contentTemplates {
                sdo {
                  id
                  data
                }
              }
            }
            primaryAsset(assetType: "media") {
              id
              jsondata
            }
            assets(assetType: "content-template") {
              records {
                id
                jsondata
                transform(transformFunction: JSON)
              }
            }
            jobs {
              records {
                id
                status
              }
            }
          }
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<{
      temporalDataObjects: {
        offset: number;
        limit: number;
        count: number;
        records: Array<RecordingMetadata>;
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoIds },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp;
  }

  async getFileMetadataByFolderId(
    folderId: string,
    offset: number,
    limit: number
  ) {
    const query = `
      query fetchFileMetadataByFolder($folderId: ID!, $offset: Int, $limit: Int) {
        folder(id: $folderId) {
          childTDOs (offset: $offset, limit: $limit) {
            offset
            limit
            count
            records {
              id
              createdDateTime
              description
              details
              name
              createdBy
              folders {
                contentTemplates {
                  sdo {
                    id
                    data
                  }
                }
              }
              primaryAsset(assetType: "media") {
                id
                jsondata
              }
              assets(assetType: "content-template") {
                records {
                  id
                  jsondata
                  transform(transformFunction: JSON)
                }
              }
              jobs {
                records {
                  id
                  status
                }
              }
            }
          }
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<{
      folder: {
        childTDOs: {
          offset: number;
          limit: number;
          count: number;
          records: Array<RecordingMetadata>;
        };
      };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { folderId, offset, limit },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp;
  }

  async updateFileMetadata({
    tdoId,
    fileMetadata,
    evidenceTypeMetadata,
    evidenceTypeSchemaId,
  }: {
    tdoId: string;
    fileMetadata: Pick<
      FileMetadata,
      'veritoneFile' | 'description' | 'aiCognitionEngineOutput' | 'sourceName'
    >;
    evidenceTypeMetadata: EvidenceTypeMetadata;
    evidenceTypeSchemaId: string;
  }) {
    const query = `
      mutation updateTDO(
        $tdoId: ID!,
        $veritoneFile: JSONData,
        $description: String!,
        $evidenceTypeMetadata: JSONData!,
        $evidenceTypeSchemaId: ID!
      ) {
        updateTDO(
           input: {
            id: $tdoId,
            details: {
              veritoneFile: $veritoneFile,
            },
            description: $description,
            contentTemplates: [{
              schemaId: $evidenceTypeSchemaId,
              data: $evidenceTypeMetadata,
            }]
          }
        ) {
          id
          details
        }
      }
    `;

    const resp = await baseGraphQLApiThrowError<{
      updateTDO: { id: string };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {
        tdoId,
        veritoneFile: fileMetadata.veritoneFile,
        description: fileMetadata.description,
        evidenceTypeMetadata,
        evidenceTypeSchemaId,
      },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp;
  }

  async fetchFileName({ tdoId }: { tdoId: string }) {
    const query = `
      query fetchFileName($tdoId: ID!) {
        temporalDataObject(id: $tdoId) {
          name
          details
        }
      }`;

    const resp = await baseGraphQLApiThrowError<{
      temporalDataObject: TemporalDataObject;
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: { tdoId },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return (
      resp.temporalDataObject?.details?.veritoneFile?.fileName ??
      resp.temporalDataObject.name
    );
  }

  async getEngineResults({
    tdoId,
    engineId,
    engineCategoryId,
    startOffsetMs,
    stopOffsetMs,
    ignoreUserEdited = false,
  }: {
    tdoId: string;
    engineId?: string;
    engineCategoryId?: string;
    startOffsetMs?: number;
    stopOffsetMs?: number;
    ignoreUserEdited?: boolean;
  }) {
    const query = `query engineResults($tdoId: ID!, $engineIds: [ID!], $engineCategoryIds: [ID!], $startOffsetMs: Int, $stopOffsetMs: Int, $ignoreUserEdited: Boolean) {
      engineResults(tdoId: $tdoId, engineIds: $engineIds, engineCategoryIds: $engineCategoryIds, startOffsetMs: $startOffsetMs, stopOffsetMs: $stopOffsetMs, ignoreUserEdited: $ignoreUserEdited) {
        records {
          tdoId
          engineId
          startOffsetMs
          stopOffsetMs
          assetId
          userEdited
          jsondata
        }
      }
    }`;

    const resp = await baseGraphQLApiThrowError<{
      engineResults: { records: EngineResult[] };
    }>({
      gqlEndpoint: this.gqlEndpoint,
      query,
      variables: {
        tdoId,
        engineIds: engineId ? [engineId] : [],
        engineCategoryIds: engineCategoryId ? [engineCategoryId] : [],
        startOffsetMs,
        stopOffsetMs,
        ignoreUserEdited,
      },
      token: this.token,
      veritoneAppId: this.veritoneAppId,
    });

    return resp.engineResults.records;
  }
}
