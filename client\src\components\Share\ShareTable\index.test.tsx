import { fireEvent, screen, waitFor } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { render } from '../../../../test/render';
import ShareTable from '.';
import { mockShares } from './mockData';

vi.mock('@tanstack/react-virtual', () => ({
  useVirtualizer: vi.fn(() => {
    const virtualItems = mockShares.map((_, index) => ({
      index,
      size: 69,
      start: index * 69,
      key: index,
      measureElement: vi.fn(),
    }));

    return {
      getVirtualItems: () => virtualItems,
      getTotalSize: () => virtualItems.length * 69,
    };
  }),
}));

const defaultProps = {
  shareMap: {},
  selected: '',
  setSelected: vi.fn(),
  handleSelect: vi.fn(),
  handleDoubleClick: vi.fn(),
  pendingDeleteIds: [],
  setPendingDeleteIds: vi.fn(),
};

describe('ShareTable Component', () => {
  it('should render the table with mock data correctly', () => {
    render(<ShareTable {...defaultProps} />);

    expect(screen.getByText('Share Name')).toBeInTheDocument();
    expect(screen.getByText('Shared by:')).toBeInTheDocument();
    expect(screen.getByText('# of Files')).toBeInTheDocument();

    const firstRowData = mockShares[0];
    expect(screen.getByText(firstRowData.shareName)).toBeInTheDocument();
    expect(screen.getAllByText(firstRowData.sharedBy)[0]).toBeInTheDocument();
    expect(screen.getAllByText(firstRowData.fileCount)[0]).toBeInTheDocument();
  });

  it('should open a delete confirmation dialog when delete icon is clicked', async () => {
    render(<ShareTable {...defaultProps} />);

    const rows = screen.getAllByRole('button', { name: /delete/i });
    const firstDeleteButton = rows[0];
    fireEvent.click(firstDeleteButton);
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Are you sure?!')).toBeInTheDocument();
    });
  });
});
