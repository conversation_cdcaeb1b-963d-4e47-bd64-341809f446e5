import { describe } from 'node:test';
import { render } from '../../../test/render';
import { expect, it, vi } from 'vitest';
import NavBar from '.';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@theme';
import {
  SettingsSliceState,
  initialState as settingInitialState,
} from '@store/modules/settings/slice';
import {
  CaseManagerSliceState,
  initialState as caseManagerInitialState,
} from '@store/modules/caseManager/slice';
import { Provider } from 'react-redux';
import { configureAppStore } from '@store/index';
import * as uploadFileEx from '@utils/files/uploadFile';

vi.mock('@components/Menu', () => ({
  default: (props: { 'data-testid': string; children: React.ReactNode }) => (
    <div data-testid={props['data-testid']}>{props.children}</div>
  ),
}));

vi.mock('@mui/material/MenuItem', () => ({
  default: (props: {
    'data-testid': string;
    onClick: () => void;
    children: React.ReactNode;
  }) => (
    <div data-testid={props['data-testid']} onClick={props.onClick}>
      {props.children}
    </div>
  ),
}));

const initialStateForMock: {
  settings: SettingsSliceState;
  caseManager: CaseManagerSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  settings: {
    ...settingInitialState,
    fetchedStatuses: {
      status: 'idle',
      error: '',
      statuses: [
        {
          id: 'key1',
          label: 'status1',
          active: true,
          color: '#00FF00',
        },
        {
          id: 'key2',
          label: 'status2',
          active: true,
          color: '#FF0000',
        },
      ],
      sdoId: '',
    },
  },
  caseManager: {
    ...caseManagerInitialState,
    rootFolder: {
      status: 'idle',
      error: '',
      id: 'rootFolder123',
    },
    folderContentTemplateSchema: {
      status: 'idle',
      error: '',
      id: 'folderContentTemplateSchemaId123',
    },
    allFolderIds: {
      status: 'idle',
      error: '',
      data: [
        { id: 'folder1', name: 'Folder 1' },
        { id: 'folder2', name: 'Folder 2' },
      ],
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};
describe('NavBar', () => {
  it('upload', async () => {
    // Mock the necessary hooks and components
    vi.mock('react-router', async () => {
      const actual = await vi.importActual('react-router');
      return {
        ...actual,
        useLocation: () => ({ pathname: '/test' }),
        useNavigate: () => vi.fn(),
      };
    });

    // uploadFile util spy
    const uploadFileSpy = vi.spyOn(uploadFileEx, 'uploadFile');

    const store = configureAppStore(initialStateForMock);

    // Render the NavBar component
    render(
      <ThemeProvider>
        <Provider store={store}>
          <NavBar />
        </Provider>
      </ThemeProvider>
    );

    expect(screen.getByTestId('upload-files-btn')).toBeInTheDocument();

    const uploadButton = screen.getByTestId('upload-files-btn');
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(uploadFileSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          selectedFolderId: expect.any(String),
          selectedCaseId: expect.any(String),
          intl: expect.anything(),
          orgFolderIds: expect.arrayContaining([
            { id: expect.any(String), name: expect.any(String) },
          ]),
        })
      );
    });
  });
});
