@import "react-date-range/dist/styles.css";
@import "react-date-range/dist/theme/default.css";

.text-field {
    background: var(--background-primary);
    padding-right: 10px;
    height: 30px;
    width: 100%;

    & > input {
        padding-right: 4px !important;
        cursor: pointer;
    }

    svg {
        font-size: 19px;
        font-weight: lighter;
        cursor: pointer;
    }

    .clear-icon {
        width: 15px;
        height: 15px;
        opacity: 0.6;
        color: var(--button-inner);
        padding: 2px;
        border-radius: 50%;
        background-color: var(--chip-background-grey);
        
        &:hover {
            opacity: 0.8;
        }
    }
}

.confirm-button {
    color: white;
    font-weight: bold;
    border-radius: 0;
}

.rdrMonth {
    width: 100%;
}
