import { describe, it } from 'vitest';
import config from '../../apiConfig.json';
import { processScheduledEvent } from '.';

describe('processScheduledEvent', () => {
  // not unit test. just a driver to help troubleshoot processScheduledEvent
  it.skip('process scheduled event', async () => {
    const token = '2dec50dc-8415-4def-b863-18c0f609daa8';
    const appConfig = config;
    const orgExpirationMap = {};
    await processScheduledEvent({
      token,
      appConfig,
      orgExpirationMap,
    });
    console.info('done =====>');
  }, 500000);
});
