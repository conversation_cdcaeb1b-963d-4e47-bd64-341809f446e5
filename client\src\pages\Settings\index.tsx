import './index.scss';
import { ColorIcon, DeleteWithLines, Eye } from '@assets/icons';
import ColorPicker from '@components/ColorPicker';
import Dialog from '@components/Dialog';
import ReorderTable from '@components/ReorderTable';
import { I18nTranslate } from '@i18n';
import {
  Add as AddIcon,
  EditNote as EditNoteIcon,
  SaveOutlined as SaveOutlinedIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  CircularProgress,
  FormControlLabel,
  Radio,
  RadioGroup,
  Tab,
  Tabs,
  TextField,
} from '@mui/material';
import { CaseStatus, CaseTag } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import {
  selectStatusSchema,
  selectTagSchema,
} from '@store/modules/config/slice';
import {
  fetchStatuses,
  fetchTags,
  pollingFetchSettingsData,
  saveStatuses,
  saveTags,
  searchCasesByStatus,
  selectEditingRows,
  selectFetchedStatuses,
  selectFetchedStatusesSdoId,
  selectFetchedStatusesStatus,
  selectFetchedTags,
  selectFetchedTagsSdoId,
  selectFetchedTagsStatus,
  selectRowSelection,
  selectSaveStatusesStatus,
  selectSaveTagsStatus,
  selectSearchCasesStatus,
  StatusTagRow,
  updateEditingRows,
  updateRowSelection,
  Visibility,
} from '@store/modules/settings/slice';
import cn from 'classnames';
import { isEqual } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useBeforeUnload, useBlocker } from 'react-router';
import { v4 as uuid } from 'uuid';
import CellColorPicker from './CellColorPicker';
import DeleteDialog from './DeleteDialog';
import EmptyState from './EmptyState';

export enum OptionsType {
  status = 'caseStatus',
  tag = 'caseTags',
}

const renderOptionTitle = (status: OptionsType) =>
  ({
    caseStatus: I18nTranslate.TranslateMessage('caseStatus'),
    caseTags: I18nTranslate.TranslateMessage('caseTagSettings'),
  })[status];

export const convertRowsStatus = (rows: StatusTagRow[]): CaseStatus[] =>
  rows.map((row) => ({
    id: row.id,
    label: row.name,
    color: row?.color ?? '#000000',
    active: row.visibility === Visibility.Active,
  }));

export const convertRowsTags = (rows: StatusTagRow[]): CaseTag[] =>
  rows.map((row) => ({
    id: row.id,
    label: row.name,
    active: row.visibility === Visibility.Active,
  }));

const convertStatusRows = (statuses: CaseStatus[]): StatusTagRow[] =>
  statuses.map((status) => ({
    id: status.id,
    name: status.label,
    color: status.color,
    visibility: status.active ? Visibility.Active : Visibility.Inactive,
  }));

const convertTagsRows = (tags: CaseTag[]): StatusTagRow[] =>
  tags.map((tag) => ({
    id: tag.id,
    name: tag.label,
    visibility: tag.active ? Visibility.Active : Visibility.Inactive,
  }));

const Settings = () => {
  const dispatch = useAppDispatch();
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [pendingTab, setPendingTab] = useState<OptionsType | null>(null);
  const currentStatuses = useSelector(selectFetchedStatuses);
  const currentStatusesSdoId = useSelector(selectFetchedStatusesSdoId);
  const statusSchemaId = useSelector(selectStatusSchema).id;
  const tagSchemaId = useSelector(selectTagSchema).id;
  const [selectedTab, setSelectedTab] = useState<OptionsType>(
    OptionsType.status
  );
  const currentStatusesStatus = useSelector(selectFetchedStatusesStatus);
  const currentTagsStatus = useSelector(selectFetchedTagsStatus);
  const currentPreconfiguredTags = useSelector(selectFetchedTags);
  const currentTagsSdoId = useSelector(selectFetchedTagsSdoId);
  const [newStatusTagName, setNewStatusTagName] = useState('');
  const [color, setColor] = useState('#4F46E5');
  const [deleteRow, setDeleteRow] = useState<StatusTagRow | null>(null);
  const [error, setError] = useState('');

  const searchCasesStatus = useSelector(selectSearchCasesStatus);
  const saveStatusesStatus = useSelector(selectSaveStatusesStatus);
  const saveTagsStatus = useSelector(selectSaveTagsStatus);
  const rowSelection = useSelector(selectRowSelection);
  const editingRows = useSelector(selectEditingRows);

  const selectedRowIds = Object.keys(rowSelection);

  // State used for update visibility / color
  const [visibilityDialogOpen, setVisibilityDialogOpen] = useState(false);
  const [activeRadio, setActiveRadio] = useState(true);
  const [openColorPicker, setOpenColorPicker] = useState(false);

  const [editStatusesSdoId, setEditStatusesSdoId] = useState('');
  const [editTagsSdoId, setEditTagsSdoId] = useState('');
  const [confirmModifiedDataDialogOpen, setConfirmModifiedDataDialogOpen] =
    useState(false);

  const colorPickerAnchor = useRef<HTMLButtonElement>(null);
  const pollFetchSettingsDataRef = useRef<DispatchPromise>(null);
  const hasData =
    (selectedTab === OptionsType.status && currentStatuses.length > 0) ||
    (selectedTab === OptionsType.tag && currentPreconfiguredTags.length > 0);
  const isDataLoading =
    selectedTab === OptionsType.status
      ? currentStatusesStatus === 'loading' || currentStatusesStatus === 'idle'
      : currentTagsStatus === 'loading' || currentTagsStatus === 'idle';
  const intl = I18nTranslate.Intl();

  const blocker = useBlocker(() => editMode && unsavedChanges);

  useBeforeUnload((e) => {
    if (editMode && unsavedChanges) {
      e.preventDefault();
    }
  });

  const setEditingRows = (rows: StatusTagRow[]) => {
    dispatch(updateEditingRows(rows));
  };

  useEffect(() => {
    if (blocker.state === 'blocked') {
      setConfirmDialogOpen(true);
    }
  }, [blocker]);

  useEffect(() => {
    if (saveStatusesStatus === 'concurrentModificationError') {
      setConfirmModifiedDataDialogOpen(true);
      setEditStatusesSdoId('');
    }
  }, [saveStatusesStatus]);

  useEffect(() => {
    if (saveTagsStatus === 'concurrentModificationError') {
      setConfirmModifiedDataDialogOpen(true);
      setEditTagsSdoId('');
    }
  }, [saveTagsStatus]);

  useEffect(() => {
    // call fetchStatuses to show loading spinner
    if (statusSchemaId) {
      if (!currentStatuses.length) {
        dispatch(fetchStatuses({}));
      }

      pollFetchSettingsDataRef.current = dispatch(pollingFetchSettingsData());
    }

    return () => {
      pollFetchSettingsDataRef.current?.abort();
    };
  }, [statusSchemaId]);

  useEffect(() => {
    if (tagSchemaId && !currentPreconfiguredTags.length) {
      dispatch(fetchTags({}));
    }
  }, [tagSchemaId]);

  const handleChangeTab = (newValue: OptionsType) => {
    if (editMode && unsavedChanges) {
      setPendingTab(newValue);
      setConfirmDialogOpen(true);
    } else {
      setSelectedTab(newValue);
      setEditMode(false);
    }
  };

  const handleSaveAddDialog = () => {
    if (selectedTab === OptionsType.status) {
      const newStatus = {
        id: uuid(),
        color,
        label: newStatusTagName,
        active: true,
      };
      if (!editMode) {
        dispatch(
          saveStatuses({
            statuses: [newStatus, ...currentStatuses],
            isAddNew: true,
          })
        );
      } else {
        const newRow = convertStatusRows([newStatus])[0];
        onDataTableChange([newRow, ...editingRows]);
      }
    } else {
      // Split the new tag name by commas and trim whitespace
      const newTagNames = newStatusTagName
        .split(',')
        .map((word) => word.trim())
        .filter(Boolean);

      const newTags = newTagNames.map((tagName) => ({
        id: uuid(),
        label: tagName,
        active: activeRadio,
      }));

      if (!editMode) {
        dispatch(
          saveTags({
            tags: [...newTags, ...currentPreconfiguredTags],
            isAddNew: true,
          })
        );
      } else {
        const newTagRows = convertTagsRows(newTags);
        onDataTableChange([...newTagRows, ...editingRows]);
      }
    }
    setAddDialogOpen(false);
  };

  const handleClose = () => {
    setAddDialogOpen(false);
    setNewStatusTagName('');
    setColor('#4F46E5');
    setError('');
  };

  const validateStatus = (newName: string, currentId?: string): string => {
    if (newName === '') {
      return '';
    }

    if (!/^[a-zA-Z0-9-_]+$/.test(newName)) {
      return intl.formatMessage({ id: 'invalidCharacterErrorMessage' });
    }

    const isDuplicate = currentStatuses.some(
      (status) => status.label === newName && status.id !== currentId
    );

    if (isDuplicate) {
      return intl.formatMessage({ id: 'statusLabelNameAlreadyTaken' });
    }

    return '';
  };

  const validateTags = (input: string) => {
    // Remove whitespace and empty strings
    const newTagNames = input
      .split(',')
      .map((word) => word.trim())
      .filter(Boolean);

    if (newTagNames.length > 10) {
      return 'enteredMoreThanTenTags';
    }

    const existingTagSet = new Set(
      currentPreconfiguredTags.map((tag) => tag.label)
    );
    const duplicateTags = [];

    for (const tag of newTagNames) {
      if (!/^[a-zA-Z0-9-_ ]+$/.test(tag)) {
        return intl.formatMessage(
          { id: 'invalidCharacterInTag' },
          { tag: `${tag}` }
        );
      }

      if (tag.length > 15) {
        return intl.formatMessage(
          { id: 'tagNameIsTooLong' },
          { tag: `${tag}` }
        );
      }

      // Check for duplicate tags
      if (existingTagSet.has(tag)) {
        duplicateTags.push(tag);
      }
    }

    if (duplicateTags.length > 0) {
      return intl.formatMessage(
        { id: 'tagNameAlreadyTaken' },
        { tags: duplicateTags.join(', ') }
      );
    }

    return '';
  };

  const handleEditStatuses = () => {
    setEditingRows(convertStatusRows(currentStatuses));
    setEditStatusesSdoId(currentStatusesSdoId);
    setEditMode(true);
    setError('');
  };

  const handleSaveStatuses = () => {
    const saveStatusesSdoId =
      editStatusesSdoId.length > 0 ? editStatusesSdoId : currentStatusesSdoId;
    const availableRows = editingRows.filter((row) => !row.isDeleted);
    for (const row of availableRows) {
      const errorMessage = validateStatus(row.name, row.id);
      if (errorMessage) {
        setError(errorMessage);
        enqueueSnackbar(errorMessage, { variant: 'error' });
        return;
      }
    }
    dispatch(
      saveStatuses({
        statuses: convertRowsStatus(availableRows),
        sdoId: saveStatusesSdoId,
      })
    ).then((payload) => {
      if (
        'error' in payload &&
        payload.error.message?.startsWith('concurrentModificationError')
      ) {
        return;
      } else {
        setEditMode(false);
        setUnsavedChanges(false);
      }
    });
    setError('');
  };

  const handleEditTags = () => {
    setEditingRows(convertTagsRows(currentPreconfiguredTags));
    setEditTagsSdoId(currentTagsSdoId);
    setEditMode(true);
  };

  const handleSaveTags = () => {
    const saveTagsSdoId =
      editTagsSdoId.length > 0 ? editTagsSdoId : currentTagsSdoId;
    const availableRows = editingRows.filter((row) => !row.isDeleted);
    dispatch(
      saveTags({
        tags: convertRowsTags(availableRows),
        sdoId: saveTagsSdoId,
      })
    ).then((payload) => {
      if (
        'error' in payload &&
        payload.error.message?.startsWith('concurrentModificationError')
      ) {
        return;
      } else {
        setEditMode(false);
        setUnsavedChanges(false);
        return null;
      }
    });
  };

  const handleAddDialogOpen = () => {
    setError('');
    setNewStatusTagName('');
    setColor('#4F46E5');
    setActiveRadio(true);
    setAddDialogOpen(true);
  };

  const handleConfirmSaveChanges = () => {
    if (selectedTab === OptionsType.status) {
      handleSaveStatuses();
    } else {
      handleSaveTags();
    }
    setConfirmModifiedDataDialogOpen(false);
    setConfirmDialogOpen(false);
    setSelectedTab(pendingTab ?? OptionsType.status);
    setPendingTab(null);
  };

  const handleCancelSaveChanges = () => {
    setConfirmDialogOpen(false);
    setPendingTab(null);
  };

  const handleCancelModifiedDataSaveChanges = () => {
    if (selectedTab === OptionsType.status) {
      dispatch(fetchStatuses({}));
    } else {
      dispatch(fetchTags({}));
    }
    setConfirmModifiedDataDialogOpen(false);
    setEditMode(false);
  };

  const handleStatusNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setNewStatusTagName(newName);

    let errorMessage = '';
    if (selectedTab === OptionsType.status) {
      errorMessage = validateStatus(newName);
    } else {
      errorMessage = validateTags(newName);
    }

    setError(errorMessage);
  };

  const renderVisibilityRadioGroup = () => (
    <RadioGroup
      row
      value={activeRadio ? 'active' : 'inactive'}
      className="settings__dialog-radio-group"
      onChange={(event) => {
        setActiveRadio(event.target.value === 'active');
      }}
    >
      <FormControlLabel
        value="active"
        control={<Radio />}
        label={I18nTranslate.TranslateMessage('active')}
        data-testid="active-radio"
      />
      <FormControlLabel
        value="inactive"
        control={<Radio />}
        label={I18nTranslate.TranslateMessage('inactive')}
        data-testid="inactive-radio"
      />
    </RadioGroup>
  );

  const renderControlButtons = () => {
    if (hasData) {
      const enableSetColor =
        editMode &&
        !!selectedRowIds.length &&
        selectedTab === OptionsType.status;
      const enableSetVisibility = editMode && !!selectedRowIds.length;

      return (
        <div className="settings__option-header-buttons">
          {enableSetColor && (
            <Button
              disabled={selectedRowIds.length === 0}
              onClick={() => {
                setOpenColorPicker(true);
                setColor('#4F46E5');
              }}
              className={cn('settings__option-header-button-text', {
                'button-clicked': openColorPicker,
              })}
              data-testid="change-color-button"
              ref={colorPickerAnchor}
            >
              <ColorIcon />
              {I18nTranslate.TranslateMessage('setColor')}
            </Button>
          )}
          {enableSetVisibility && (
            <>
              <Button
                disabled={selectedRowIds.length === 0}
                onClick={() => {
                  setVisibilityDialogOpen(true);
                  setActiveRadio(true);
                }}
                className={cn('settings__option-header-button-text', {
                  'button-clicked': visibilityDialogOpen,
                })}
                data-testid="change-visibility-button"
              >
                <Eye />
                {I18nTranslate.TranslateMessage('setVisibility')}
              </Button>
              <Button
                className="settings__option-header-button-text"
                onClick={handleDeleteSelectedRows}
                data-testid="delete-status-button"
              >
                {searchCasesStatus === 'loading' &&
                !deleteRow &&
                !!selectedRowIds.length ? (
                  <CircularProgress size={18} color="inherit" />
                ) : (
                  <DeleteWithLines />
                )}
                {I18nTranslate.TranslateMessage('delete')}
              </Button>
            </>
          )}
          {editMode && (
            <Button
              onClick={() => setEditMode(!editMode)}
              data-testid="cancel-button"
            >
              {I18nTranslate.TranslateMessage('cancel')}
            </Button>
          )}
          <Button
            className={cn('settings__option-header-button', {
              editMode: editMode && unsavedChanges,
              disabled: editMode && !unsavedChanges,
            })}
            onClick={() =>
              editMode
                ? selectedTab === OptionsType.status
                  ? handleSaveStatuses()
                  : handleSaveTags()
                : selectedTab === OptionsType.status
                  ? handleEditStatuses()
                  : handleEditTags()
            }
            disabled={editMode && !unsavedChanges}
            color="primary"
            data-testid="edit-button"
          >
            {saveTagsStatus === 'loading' ||
            saveStatusesStatus === 'loading' ? (
              <CircularProgress size={18} sx={{ mr: 1 }} color="inherit" />
            ) : editMode ? (
              <SaveOutlinedIcon />
            ) : (
              <EditNoteIcon />
            )}
            {editMode
              ? I18nTranslate.TranslateMessage('saveChanges')
              : selectedTab === OptionsType.status
                ? I18nTranslate.TranslateMessage('editStatusLabels')
                : I18nTranslate.TranslateMessage('editTags')}
          </Button>
          <Button
            data-testid="add-button"
            className="settings__option-header-button"
            onClick={handleAddDialogOpen}
            color="primary"
          >
            <AddIcon />
            {selectedTab === OptionsType.status
              ? I18nTranslate.TranslateMessage('addStatus')
              : I18nTranslate.TranslateMessage('addTag')}
          </Button>
        </div>
      );
    }
  };

  const handleDeleteSelectedRows = () => {
    if (selectedTab === OptionsType.status) {
      dispatch(searchCasesByStatus(selectedRowIds)).then(() =>
        setDeleteDialogOpen(true)
      );
    } else {
      setDeleteDialogOpen(true);
    }
  };

  const onDataTableChange = (rows: StatusTagRow[]) => {
    let rowsEqual = true;
    const isDeleted = rows.some((row) => row.isDeleted);
    // Find out if the data has changed
    if (selectedTab === OptionsType.status) {
      const compareRows = convertRowsStatus(rows);
      rowsEqual = isEqual(compareRows, currentStatuses);
    }
    if (selectedTab === OptionsType.tag) {
      const compareRows = convertRowsTags(rows);
      rowsEqual = isEqual(compareRows, currentPreconfiguredTags);
    }
    if (isDeleted) {
      rowsEqual = false;
    }
    setUnsavedChanges(!rowsEqual);
    setEditingRows(rows);
  };

  const handleCloseVisibilityModal = () => {
    setVisibilityDialogOpen(false);
    setActiveRadio(true);
  };

  const updatedVisibility = (): StatusTagRow[] => {
    const visibility = activeRadio ? Visibility.Active : Visibility.Inactive;
    return editingRows.map((row) => ({
      ...row,
      visibility: rowSelection[row.id] ? visibility : row.visibility,
    }));
  };

  const handleConfirmVisibilityModal = () => {
    const newEditingRows = updatedVisibility();
    onDataTableChange(newEditingRows);
    handleCloseVisibilityModal();
  };

  const handleChangeColor = (color: string) => {
    const newEditingRows = editingRows.map((row) => ({
      ...row,
      color: rowSelection[row.id] ? color : row.color,
    }));
    onDataTableChange(newEditingRows);
    setColor(color);
  };

  const handleDeleteRows = (rowIds: string[]) => {
    const rowIdsSet = new Set(rowIds);
    const newEditingRows = editingRows.map((row) => ({
      ...row,
      isDeleted: rowIdsSet.has(row.id),
      selected: false,
    }));
    onDataTableChange(newEditingRows);
    dispatch(updateRowSelection({}));
  };
  const isOptionsType = (value: unknown): value is OptionsType =>
    Object.values(OptionsType).includes(value as OptionsType);

  return (
    <>
      <div className="settings">
        <div className="settings__header">
          <div className="settings__title">
            {I18nTranslate.TranslateMessage('settings')}
          </div>
        </div>
        <div className="settings__content" data-testid="settings-content">
          <div className="settings__tabs">
            <div className="settings__tabs-description">
              {I18nTranslate.TranslateMessage('belowYouCanFindKeySettings')}
            </div>
            <Box className="settings__tabs-container">
              <Tabs
                value={selectedTab}
                orientation="vertical"
                onChange={(_e, newValue) => {
                  if (isOptionsType(newValue)) {
                    handleChangeTab(newValue);
                  }
                }}
              >
                <Tab
                  label={I18nTranslate.TranslateMessage('caseStatus')}
                  value="caseStatus"
                  className={cn('settings__tab', {
                    selected: selectedTab === OptionsType.status,
                  })}
                  data-testid="status-labels-tab"
                />
                <Tab
                  label={I18nTranslate.TranslateMessage('caseTags')}
                  value="caseTags"
                  className={cn('settings__tab', {
                    selected: selectedTab === OptionsType.tag,
                  })}
                  data-testid="preconfigured-tags-tab"
                />
              </Tabs>
            </Box>
          </div>
          <div className="settings__option-container">
            <div className="settings__option-header">
              {renderOptionTitle(selectedTab)}
              {renderControlButtons()}
            </div>
            <ReorderTable
              className="settings__reorder-table"
              data={
                editMode
                  ? structuredClone(editingRows)
                  : selectedTab === OptionsType.status
                    ? convertStatusRows(currentStatuses)
                    : convertTagsRows(currentPreconfiguredTags)
              }
              editMode={editMode}
              onChange={(rows) => {
                onDataTableChange(rows);
              }}
              isLoading={isDataLoading}
              colorRow={selectedTab === OptionsType.status}
              type={selectedTab === OptionsType.status ? 'Status' : 'Tag'}
              deleteRow={deleteRow}
              setDeleteRow={setDeleteRow}
              validateName={
                selectedTab === OptionsType.status
                  ? validateStatus
                  : validateTags
              }
              setUnsavedChanges={setUnsavedChanges}
              emptyState={
                <EmptyState
                  option={selectedTab}
                  onClick={handleAddDialogOpen}
                />
              }
            />
          </div>
        </div>
      </div>
      {/* create tag/status dialog */}
      <Dialog
        open={addDialogOpen}
        onClose={handleClose}
        onConfirm={() => {
          const errorMessage =
            selectedTab === OptionsType.status
              ? validateStatus(newStatusTagName)
              : validateTags(newStatusTagName);

          if (errorMessage) {
            setError(errorMessage);
            return;
          }

          handleSaveAddDialog();
          setAddDialogOpen(false);
          setError('');
        }}
        title={
          selectedTab === OptionsType.status
            ? intl.formatMessage({ id: 'addNewStatus' })
            : intl.formatMessage({ id: 'addNewTag' })
        }
        disableConfirm={!newStatusTagName || !!error}
        confirmText={
          selectedTab === OptionsType.status
            ? intl.formatMessage({ id: 'addStatus' })
            : newStatusTagName
                  .split(',')
                  .map((word) => word.trim())
                  .filter(Boolean).length > 1
              ? intl.formatMessage({ id: 'addTags' })
              : intl.formatMessage({ id: 'addTag' })
        }
        cancelText={intl.formatMessage({ id: 'cancel' })}
      >
        <div className="settings__dialog-description">
          {selectedTab === OptionsType.status
            ? I18nTranslate.TranslateMessage('nameStatusAndAssignColor')
            : I18nTranslate.TranslateMessage('nameTagAndChooseVisibility')}
        </div>
        <Box display="flex" flexDirection="column" gap={1}>
          <Box display="flex" gap={4} alignItems="start">
            {selectedTab === OptionsType.status ? (
              <Box flex="1" display="flex" flexDirection="column" gap={1}>
                <div className="settings__dialog-label">
                  {I18nTranslate.TranslateMessage('color')}
                </div>
                <CellColorPicker
                  className="settings__dialog-color-picker"
                  color={color}
                  onChange={(value) => {
                    setColor(value);
                  }}
                  anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                  }}
                  transformOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                  }}
                  isCreateStatus
                />
              </Box>
            ) : null}
            <Box className="settings__dialog-text-field-container">
              {selectedTab === OptionsType.status ? (
                <div className="settings__dialog-label">
                  {I18nTranslate.TranslateMessage('statusName')}
                </div>
              ) : (
                <div className="settings__dialog-label">
                  {I18nTranslate.TranslateMessage('tagName')}
                </div>
              )}
              <TextField
                fullWidth
                multiline={selectedTab === OptionsType.tag}
                rows={selectedTab === OptionsType.tag ? 3 : undefined}
                value={newStatusTagName}
                className={cn('settings__dialog-text-field', {
                  error: !!error,
                })}
                onChange={handleStatusNameChange}
                error={!!error}
                slotProps={{
                  htmlInput: {
                    'data-testid': `${selectedTab === OptionsType.status ? 'status' : 'tag'}-name-input`,
                  },
                }}
                sx={{
                  border: error
                    ? '1px solid var(--button-destructive-action)'
                    : undefined,
                  borderRadius: '4px',
                }}
              />
              {error && (
                <div
                  className="settings__dialog-error"
                  data-testid="error-message"
                >
                  {error}
                </div>
              )}
            </Box>
          </Box>
        </Box>
        {selectedTab === OptionsType.tag ? renderVisibilityRadioGroup() : null}
      </Dialog>
      {/* Set visibility dialog */}
      <Dialog
        open={visibilityDialogOpen}
        onClose={handleCloseVisibilityModal}
        title={
          selectedTab === OptionsType.tag
            ? intl.formatMessage({ id: 'setTagVisibility' })
            : intl.formatMessage({ id: 'setStatusVisibility' })
        }
        onConfirm={handleConfirmVisibilityModal}
        disableConfirm={false}
        confirmText={intl.formatMessage({ id: 'apply' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
      >
        <div>
          {selectedTab === OptionsType.tag
            ? I18nTranslate.TranslateMessage(
                'selectedTagsWillAllBeSetToThisOption'
              )
            : I18nTranslate.TranslateMessage(
                'selectedStatusesWillAllBeSetToThisOption'
              )}
        </div>
        {renderVisibilityRadioGroup()}
      </Dialog>
      {/* save change confirm dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={handleCancelSaveChanges}
        onConfirm={handleConfirmSaveChanges}
        title={intl.formatMessage({ id: 'saveChanges' })}
        disableConfirm={false}
        confirmText={intl.formatMessage({ id: 'saveChanges' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
      >
        <div className="settings__dialog-save-changes">
          {I18nTranslate.TranslateMessage(
            'wouldYouLikeToSaveYourChangesBeforeClosing'
          )}
        </div>
      </Dialog>
      <Dialog
        open={confirmModifiedDataDialogOpen}
        onClose={handleCancelModifiedDataSaveChanges}
        onConfirm={() => {
          setConfirmModifiedDataDialogOpen(false);
          if (selectedTab === OptionsType.status) {
            handleSaveStatuses();
          } else {
            handleSaveTags();
          }
        }}
        title={intl.formatMessage({ id: 'dataModified' })}
        disableConfirm={false}
        confirmText={intl.formatMessage({ id: 'saveChanges' })}
        cancelText={intl.formatMessage({ id: 'discardChanges' })}
      >
        <div className="settings__dialog-save-changes">
          {I18nTranslate.TranslateMessage(
            selectedTab === OptionsType.status
              ? 'saveModifiedStatusesMessage'
              : 'saveModifiedTagsMessage'
          )}
        </div>
      </Dialog>
      <DeleteDialog
        open={deleteDialogOpen}
        rows={editingRows}
        selectedTab={selectedTab}
        onClose={() => setDeleteDialogOpen(false)}
        handleDeleteRows={handleDeleteRows}
      />
      <ColorPicker
        anchor={colorPickerAnchor}
        open={openColorPicker}
        toggle={() => setOpenColorPicker(false)}
        onChange={handleChangeColor}
        color={color}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      />
    </>
  );
};

export default Settings;
