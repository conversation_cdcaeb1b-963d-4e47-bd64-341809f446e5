{"name": "investigate-backend", "version": "1.0.0", "scripts": {"start": "node dist/server.js", "start:dev": "ts-node-dev src/server.ts", "start:debug": "ts-node-dev --inspect-brk -- src/server.ts", "build": "rimraf dist & tsc", "lint": "yarn lint:es && yarn lint:tsc", "lint:es": "eslint . --max-warnings 0", "lint:tsc": "tsc --noEmit", "format": "prettier  src --write", "test": "vitest"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"body-parser": "^2.2.0", "express": "^5.1.0", "lodash": "^4.17.21"}, "devDependencies": {"@eslint/js": "^9.25.1", "@types/express": "^5.0.1", "@types/lodash": "^4", "@types/node": "^22.15.31", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "prettier": "^3.5.3", "rimraf": "^6.0.1", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vitest": "^3.1.2"}}