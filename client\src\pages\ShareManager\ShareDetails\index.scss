.share-details {
  height: 100%;
  min-width: 644px;
  overflow-y: auto;
  border-radius: 8px;
  background-color: var(--background-tertiary);

  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);

  display: flex;
  flex-direction: column;

  &__body {
    padding: 30px 40px 30px 40px;

    &::-webkit-scrollbar {
      width: 8px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--scrollbar-thumb);
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #9e9e9e;
    }

    &::-webkit-scrollbar-button {
      display: none;
      width: 0;
      height: 0;
    }
  }

  &__view-btn {
    height: 40px;
    padding: 10px 25px;
    color: var(--button-text-submit);
    background: var(--button-dark-blue);
  }

  &__title {
    margin-top: 20px;

    &-text {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 0;
      letter-spacing: 0px;
      color: var(--text-primary);
    }
  }

  &__status {
    gap: 10px;
    display: flex;
    margin-top: 14px;
    align-items: center;

    &-text {
      margin: 0;
      font-size: 12px;
      font-weight: 400;
      letter-spacing: 0px;
      color: var(--text-secondary);
    }
  }

  &__access {
    margin-top: 32px;

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 5px;
      padding: 0;
      color: var(--text-primary);

      &-info {
        display: flex;
        flex-direction: column;
        gap: 5px;

        & > .share-details__access-header-title {
          margin: 0;
          font-weight: 650;
          font-size: 12px;
          letter-spacing: 0px;
        }

        & > .share-details__access-header-subtitle {
          margin: 0;
          font-size: 12px;
          letter-spacing: 0px;
          font-weight: normal;
        }
      }

      &-action {
        gap: 1px;
        display: flex;
        align-items: center;
        justify-content: center;

        & > .share-details__access-header-btn {
          height: 32px;
          font-size: 12px;
          font-weight: 700;
          margin-right: 62px;
          color: var(--text-primary);
          text-transform: capitalize;

          &:disabled,
          &.Mui-disabled {
            border: none;
            color: var(--text-disabled);
            background-color: transparent;
            cursor: not-allowed;
          }
        }
      }
    }

    &-list {
      gap: 2px;
      display: flex;
      flex-direction: column;
      margin-top: 23px;
      box-sizing: border-box;
      margin-left: -40px;
      margin-right: -40px;
      padding-left: 25px;
      padding-right: 25px;

      &-item {
        width: 100%;
        height: 46px;
        padding: 13px 15px;
        display: flex;
        align-items: center;
        border: none;
        box-sizing: border-box;
        border-radius: 2px;
        background-color: var(--background-secondary);

        &:hover {
          cursor: pointer;
          background-color: var(--background-selected);
        }

        &-name {
          width: 150px;
          font-weight: bold;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &-email {
          flex-grow: 1;
          width: 150px;
          font-size: 14px;
          color: var(--text-primary);
        }

        &-status {
          text-transform: uppercase;
          margin-left: 60px;
          margin-right: 53px;
          font-size: 10px;
          font-weight: 700;
          color: #1b5e20;
        }

        &:hover,
        &:focus {
          .share-details__access-list-item-moreIcon {
            visibility: visible;
            color: var(--button-inner);
          }
        }

        &-moreIcon {
          width: 15px;
          height: 3px;
          padding: 15px;
          visibility: hidden;
          transform: rotate(90deg);
          color: var(--text-primary);
        }
      }
    }
  }

  &__additional-info {
    margin-top: 37px;
    margin-left: -40px;
    margin-right: -40px;
    padding-left: 25px;
    padding-right: 25px;

    &-title {
      margin: 0;
      font-weight: 650;
      margin-left: 15px;
      letter-spacing: 0px;
      font-size: 12px;
      color: var(--text-primary);
    }

    &-list {
      gap: 2px;
      margin-top: 22px;
      display: flex;
      flex-direction: column;
    }

    &-item {
      width: 100%;
      height: 46px;
      padding: 13px 15px;
      display: flex;
      align-items: center;
      border: none;
      border-radius: 2px;
      box-sizing: border-box;
      background-color: var(--background-secondary);

      &:hover {
        cursor: pointer;
        background-color: var(--background-selected);
      }

      &-label {
        width: 150px;
        font-size: 14px;
        font-weight: 700;
      }

      &-value {
        font-size: 14px;
        color: var(--text-primary);
      }

      &-icon {
        margin-left: 20px;
        color: var(--text-primary);

        &:hover {
          cursor: pointer;
          color: var(--button-inner);
        }
      }
    }
  }
  &__menu-paper {
    width: 160px;
    border-radius: 2px;
    box-shadow: none;
    margin-left: 37px;
    background-color: var(--background-tertiary);
  }
}
