import './index.scss';
import { RemoveMinus } from '@assets/icons';
import { I18nTranslate } from '@i18n';
import { Button, IconButton, TextField } from '@mui/material';

interface NewUserInfo {
  firstName: string;
  lastName: string;
  email: string;
}

interface AddUserRowProps {
  userInfo: NewUserInfo;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSend: () => void;
  onCancel: () => void;
  isSendDisabled: boolean;
}

const AddUserRow = ({
  userInfo,
  onInputChange,
  onSend,
  onCancel,
  isSendDisabled,
}: AddUserRowProps) => {
  const intl = I18nTranslate.Intl();
  return (
    <div id="add-share-user" className="add-user-row">
      <TextField
        name="firstName"
        value={userInfo.firstName}
        onChange={onInputChange}
        placeholder={intl.formatMessage({ id: 'firstName' })}
        variant="outlined"
        size="small"
        className="add-user-row__input"
      />
      <TextField
        name="lastName"
        value={userInfo.lastName}
        onChange={onInputChange}
        placeholder={intl.formatMessage({ id: 'lastName' })}
        variant="outlined"
        size="small"
        className="add-user-row__input"
      />
      <TextField
        name="email"
        value={userInfo.email}
        onChange={onInputChange}
        placeholder={intl.formatMessage({ id: 'emailAddress' })}
        variant="outlined"
        size="small"
        className="add-user-row__input--email"
      />
      <Button
        variant="outlined"
        onClick={onSend}
        className="add-user-row__send-btn"
        disabled={isSendDisabled}
      >
        {intl.formatMessage({ id: 'sendButton' })}
      </Button>
      <IconButton onClick={onCancel} className="add-user-row__remove-btn">
        <RemoveMinus />
      </IconButton>
    </div>
  );
};

export default AddUserRow;
