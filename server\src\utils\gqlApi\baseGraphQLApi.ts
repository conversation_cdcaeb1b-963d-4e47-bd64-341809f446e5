import { isEmpty } from 'lodash';

interface GQLResponse<T> {
  data: T;
  errors?: Error[];
}

/**
 * Base Graphql api for common usage.
 *
 * @param gqlEndpoint
 * @param token
 * @param query
 * @param variables
 * @param operationName
 * @param veritoneAppId veritone application id
 * @param extraHeaders
 */
export async function baseGraphQLApi<T>({
  gqlEndpoint,
  token,
  query,
  variables,
  operationName,
  veritoneAppId,
  extraHeaders,
}: {
  gqlEndpoint: string;
  token?: string;
  query: string;
  variables?: Record<string, unknown>;
  operationName?: unknown;
  veritoneAppId?: string;
  extraHeaders?: T;
}): Promise<GQLResponse<T>> {
  const response = await fetch(gqlEndpoint, {
    method: 'post',
    body: JSON.stringify({
      query,
      variables,
      operationName,
    }),
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...extraHeaders,
      ...(veritoneAppId && { 'x-veritone-application': veritoneAppId }),
    },
  });

  const resp = await response.json();
  return resp;
}

/**
 * Base Graphql api throws error when there is graphql errors in response.
 * @param gqlEndpoint
 * @param token
 * @param query
 * @param variables
 * @param operationName
 * @param veritoneAppId veritone application id
 */
export async function baseGraphQLApiThrowError<T = unknown>({
  gqlEndpoint,
  token,
  query,
  variables,
  operationName,
  veritoneAppId,
}: {
  gqlEndpoint: string;
  token: string;
  query: string;
  variables?: Record<string, unknown>;
  operationName?: string;
  veritoneAppId?: string;
}): Promise<T> {
  const resp = await baseGraphQLApi<T>({
    query,
    variables,
    operationName,
    gqlEndpoint,
    token,
    veritoneAppId,
  });
  if (!isEmpty(resp.errors)) {
    throw new Error(
      (resp?.errors || [])[0]?.message || operationName || 'Error'
    );
  }
  return resp.data;
}
