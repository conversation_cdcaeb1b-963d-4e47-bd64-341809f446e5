import { I18nTranslate } from '@i18n';
import { enqueueSnackbar } from 'notistack';

interface UploadFileParams {
  selectedFolderId?: string;
  selectedCaseId?: string;
  // TODO: IS registryId supposed to be required?
  // What should happen if it's not defined?
  registryId?: string;
  rowIdTableContextMenu?: string;
  intl: ReturnType<typeof I18nTranslate.Intl>;
  orgFolderIds: {
    id: string;
    name: string;
  }[];
}

export function uploadFile({
  selectedFolderId,
  selectedCaseId,
  intl,
  orgFolderIds,
}: UploadFileParams) {
  if (window.aiware) {
    window.aiware.mountPanel({
      panelId: 'DATA_CENTER_IMPORTER',
      microFrontend: {
        name: 'DATA_CENTER_IMPORTER',
        config: {
          title: '',
          fileLimit: 10,
          titleSubText: '',
          allowedFolders: orgFolderIds,
          name: intl.formatMessage({ id: 'fileUploader' }),
          panelTitle: intl.formatMessage({ id: 'uploadFiles' }),
          locationFolderTitle: intl.formatMessage({ id: 'chooseACase' }),
          locationFolderSubText: intl.formatMessage({
            id: 'selectStoredLocation',
          }),
          locationFolderInputLabel: intl.formatMessage({ id: 'case' }),
          hidePermissionsManagement: true,
          hideNewFolderButton: true,
          activeFolder: selectedFolderId
            ? {
                id: selectedFolderId,
                name: selectedCaseId,
              }
            : orgFolderIds.length > 0
              ? orgFolderIds[0]
              : undefined,
        },
      },
      panelConfig: {
        dimmed: 0,
        zIndex: 1000,
        marginTop: 0,
        size: 'large',
        marginStart: 0,
        type: 'APP_BAR_PANEL_TEMPLATE',
      },
    });
  } else {
    enqueueSnackbar(
      <div>
        <strong>{intl.formatMessage({ id: 'unableToUploadFile' })}</strong>
        {/* TODO: Create custom snackbar for consistency */}
        <div className="snackbar-subtitle">
          {intl.formatMessage({ id: 'refreshPage' })}
        </div>
      </div>,
      {
        variant: 'error',
      }
    );
  }
}
