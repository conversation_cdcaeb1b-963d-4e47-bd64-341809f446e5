import './index.scss';
import { AddToCase, EditAttributes } from '@assets/icons';
import Menu from '@components/Menu';
import { I18nTranslate } from '@i18n';
import {
  DeleteOutlined as DeleteOutlinedIcon,
  DescriptionOutlined as DescriptionOutlinedIcon,
  DriveFileMoveOutlined as DriveFileMoveOutlinedIcon,
  FolderOpenOutlined as FolderOpenOutlinedIcon,
  ImageOutlined as ImageOutlinedIcon,
  MoreHoriz as MoreHorizIcon,
  VideocamOutlined as VideocamOutlinedIcon,
  VisibilityOutlined as VisibilityOutlinedIcon,
  VolumeDownOutlined as VolumeDownOutlinedIcon,
} from '@mui/icons-material';
import { Box, Checkbox, Divider, IconButton, MenuItem } from '@mui/material';
import cn from 'classnames';
import { Duration } from 'luxon';
import { useState } from 'react';

const renderFileType = (mimeType: string) => {
  if (!mimeType) {
    return <DescriptionOutlinedIcon />;
  }
  const fileType = mimeType.match(/^([^\/]+)\//)?.[1];
  switch (fileType) {
    case 'video':
      return <VideocamOutlinedIcon />;
    case 'image':
      return <ImageOutlinedIcon />;
    case 'audio':
      return <VolumeDownOutlinedIcon />;
    default:
      return <DescriptionOutlinedIcon />;
  }
};

const FileCard = ({
  fileId,
  fileName,
  fileDuration,
  caseId,
  dateUploaded,
  thumbnailUrl,
  fileType,
  blurred,
  isChecked,
  onCheck,
  onMove,
  onOpenEditMetadataDrawer,
  onViewFile,
  onViewCase,
  onDelete,
  isDefaultThumbnail,
  index,
  isPending,
}: FileCardProps) => {
  const intl = I18nTranslate.Intl();
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);

  const handleOpenEditDrawer = () => {
    setMenuAnchorEl(null);
    onOpenEditMetadataDrawer(fileId);
  };

  return (
    <div className={cn({ 'disable-file-card': isPending })}>
      <Box
        className="file-card"
        data-testid="file-card"
        display="flex"
        flexDirection="column"
        tabIndex={0}
        data-index={index}
      >
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          className="file-card-image"
          data-testid="file-card-image"
        >
          <img
            alt=""
            style={
              isDefaultThumbnail
                ? { height: '100%' }
                : { width: 40, height: 40 }
            }
            src={thumbnailUrl}
            className={cn({ blurred })}
            data-testid="file-card-image-img"
            draggable={false}
          />
          <Checkbox
            checked={isChecked}
            className="file-card-checkbox"
            onClick={(event) => {
              event.stopPropagation();
              onCheck(fileId);
            }}
          />
        </Box>
        <Box
          className="file-card-strip"
          display="flex"
          justifyContent="space-between"
          alignItems="center"
        >
          {renderFileType(fileType)}
          <Box className="file-card-strip-duration">
            {fileDuration > -1
              ? Duration.fromMillis(fileDuration).toFormat('hh:mm:ss.SS')
              : ''}
          </Box>
        </Box>
        <Box className="file-card-info" flex={1}>
          <Box className="file-card-info-name" onClick={onViewFile}>
            {fileName}
          </Box>
          <Box display="flex" flexWrap="wrap">
            <Box className="info-title">
              {intl.formatMessage({ id: 'caseIdWithColons' })}
            </Box>
            <Box className="file-card-info-case-id">
              {caseId || intl.formatMessage({ id: 'defaultEmpty' })}
            </Box>
          </Box>
          <Box display="flex" flexWrap="wrap">
            <Box className="info-title">
              {intl.formatMessage({ id: 'uploadedWithColons' })}
            </Box>
            <Box className="file-card-info-uploaded">{dateUploaded}</Box>
          </Box>
        </Box>
        <Box
          display="flex"
          justifyContent="flex-end"
          className="file-card-menu"
        >
          <IconButton
            onClick={(e) => setMenuAnchorEl(e.currentTarget)}
            data-testid="file-card-menu-icon"
          >
            <MoreHorizIcon />
          </IconButton>
        </Box>
      </Box>
      <Menu
        size="small"
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={() => setMenuAnchorEl(null)}
      >
        <MenuItem onClick={onMove} disableRipple>
          {caseId ? <DriveFileMoveOutlinedIcon /> : <AddToCase />}
          {intl.formatMessage({ id: caseId ? 'move' : 'addToCase' })}
        </MenuItem>
        <MenuItem onClick={handleOpenEditDrawer} disableRipple>
          <EditAttributes />
          {intl.formatMessage({ id: 'editMetadata' })}
        </MenuItem>
        <MenuItem onClick={onViewFile} disableRipple>
          <VisibilityOutlinedIcon />
          {intl.formatMessage({ id: 'viewFile' })}
        </MenuItem>
        {onViewCase && (
          <MenuItem onClick={onViewCase} disableRipple>
            <FolderOpenOutlinedIcon />
            {intl.formatMessage({ id: 'viewCase' })}
          </MenuItem>
        )}
        <Divider />
        <MenuItem onClick={onDelete} disableRipple>
          <DeleteOutlinedIcon />
          {intl.formatMessage({ id: 'delete' })}
        </MenuItem>
      </Menu>
    </div>
  );
};

interface FileCardProps {
  index: number;
  fileId: string;
  fileName: string;
  fileDuration: number;
  caseId?: string;
  dateUploaded: string;
  thumbnailUrl: string;
  fileType: string; // 'video' | 'audio' | 'image';
  blurred: boolean;
  isChecked: boolean;
  onCheck: (fileId: string) => void;
  onMove: () => void;
  onOpenEditMetadataDrawer: (rowId: string) => void;
  onViewFile: () => void;
  onViewCase?: () => void;
  onDelete: () => void;
  isDefaultThumbnail?: boolean;
  isPending?: boolean;
}

export default FileCard;
