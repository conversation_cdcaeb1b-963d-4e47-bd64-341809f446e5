import './index.scss';
import GroupedView from '@components/Search/SearchContents/GroupedView';
import SearchTable from '@components/Search/SearchContents/SearchTable';
import { CircularProgress } from '@mui/material';
import { useAppDispatch } from '@store/hooks';
import { SearchMediaResponse } from '@store/modules/search/searchFiles';
import {
  ResultCategory,
  searchFiles,
  selectSearchFiles,
  selectSearchView,
} from '@store/modules/search/slice';
import { convertFileType, FILE_TYPE_ICON_MAP } from '@utils/files';
import { SearchView } from '@utils/local-storage';
import { JSX } from 'react';
import { useSelector } from 'react-redux';

export interface MappedSearchResult {
  id: string;
  fileName: string;
  fileType: string;
  fileIcon: JSX.Element;
  duration: number;
  parentTreeObjectIds: string[];
  createdByName: string;
  createdTime: string;
  updatedTime: string;
  caseId?: string;
  retentionDate: string;
  description: string;
  programLiveImage: string;
}

export const mapSearchToResult = (
  results?: SearchMediaResponse
): MappedSearchResult[] =>
  results?.searchMedia.jsondata.results.map((item) => {
    let rawFileType: string | undefined = item.recording.fileType;

    if (!rawFileType) {
      rawFileType = item.context?.find((c) => c?.['veritone-file']?.mimetype)?.[
        'veritone-file'
      ]?.mimetype;
    }

    const duration: number =
      item.context?.find((c) => c?.['veritone-file']?.duration !== undefined)?.[
        'veritone-file'
      ]?.duration ?? -1;
    const fileType = convertFileType(rawFileType);
    const FileIcon = FILE_TYPE_ICON_MAP[fileType];

    return {
      id: item.recording.recordingId,
      fileName:
        item.context?.find((c) => c?.['veritone-file'])?.['veritone-file']
          ?.filename ?? '',
      fileType:
        item.context?.find((c) => c?.['veritone-file'])?.['veritone-file']
          ?.mimetype ?? '',
      fileIcon: <FileIcon />,
      duration,
      parentTreeObjectIds: item.recording.parentTreeObjectIds,
      createdByName: item.recording.creator,
      createdTime: item.recording.createdTime
        ? new Date(item.recording.createdTime).toISOString()
        : '',
      updatedTime: item.recording.modifiedTime
        ? new Date(item.recording.modifiedTime).toISOString()
        : '',
      caseId: item.recording.caseId?.trim() || undefined,
      retentionDate: '',
      description: item.recording.description || '',
      programLiveImage: item.recording.programLiveImage,
    };
  }) ?? [];

const SearchContents = (props: Props) => {
  const { pendingDeleteIds = [], setPendingDeleteIds } = props;
  const searchFilesState = useSelector(selectSearchFiles);
  const searchView = useSelector(selectSearchView);
  const dispatch = useAppDispatch();

  const ungroupedSearchResults = mapSearchToResult(
    searchFilesState.ungroupedSearch.data
  );

  const ungroupedSearchLoading =
    searchFilesState.ungroupedSearch.status === 'loading';

  const { from, to, limit, totalResults } = searchFilesState.ungroupedSearch
    .data?.searchMedia.jsondata ?? {
    from: 0,
    to: 1,
    limit: 50,
    totalResults: {
      value: 0,
      relation: '',
    },
  };

  const onPageChange = (page: number, category?: ResultCategory) => {
    if (searchFilesState.searchParams && searchView === SearchView.UnGrouped) {
      const limit = searchFilesState?.ungroupedSearch?.pagination?.limit ?? 50;
      dispatch(
        searchFiles({
          params: {
            ...searchFilesState.searchParams,
            pagination: {
              ungrouped: {
                offset: page * limit,
                limit: limit,
              },
            },
          },
        })
      );
    }
    if (
      searchFilesState.searchParams &&
      searchView === SearchView.Grouped &&
      category
    ) {
      const limit =
        searchFilesState?.searchParams?.pagination?.[category]?.limit ?? 50;
      dispatch(
        searchFiles({
          params: {
            ...searchFilesState.searchParams,
            pagination: {
              [category]: {
                offset: page * limit,
                limit: limit,
                isUpdate: true,
              },
            },
          },
        })
      );
    }
  };

  const onPageSizeChange = (pageSize: number, category?: string) => {
    if (searchFilesState.searchParams && searchView === SearchView.UnGrouped) {
      dispatch(
        searchFiles({
          params: {
            ...searchFilesState.searchParams,
            pagination: {
              ungrouped: {
                offset: 0,
                limit: pageSize,
              },
            },
          },
        })
      );
    }

    if (
      searchFilesState.searchParams &&
      searchView === SearchView.Grouped &&
      category
    ) {
      dispatch(
        searchFiles({
          params: {
            ...searchFilesState.searchParams,
            pagination: {
              [category]: {
                offset: 0,
                limit: pageSize,
                isUpdate: true,
              },
            },
          },
        })
      );
    }
  };

  return (
    <div className="content-wrapper">
      {ungroupedSearchLoading && (
        <div className="search-contents__loading">
          <CircularProgress />
        </div>
      )}
      {!ungroupedSearchLoading && searchView === SearchView.UnGrouped && (
        <SearchTable
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          searchResults={ungroupedSearchResults}
          pagination={{ from, to, limit, totalResults }}
          loading={ungroupedSearchLoading}
          pendingDeleteIds={pendingDeleteIds}
          setPendingDeleteIds={setPendingDeleteIds}
        />
      )}
      {searchView === SearchView.Grouped && (
        <GroupedView
          groupedResults={searchFilesState.groupedSearch}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          pendingDeleteIds={pendingDeleteIds}
          setPendingDeleteIds={setPendingDeleteIds}
        />
      )}
    </div>
  );
};

interface Props {
  pendingDeleteIds: string[];
  setPendingDeleteIds: (ids: string[]) => void;
}

export default SearchContents;
