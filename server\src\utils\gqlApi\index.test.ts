import { describe, expect, it } from 'vitest';
import { GQLApi } from './';
import config from '../../../apiConfig.json';

// not unit test. just a driver to help troubleshoot gql api
describe('gql api client for test', () => {
  const endpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
  const veritoneAppId = config.veritoneAppId;
  const token = '8716a638-0cdd-49e6-8d68-e4c38134e98b';
  const gql = new GQLApi(endpoint, token, veritoneAppId);

  it.skip('getSDOSchemaId', async () => {
    const dataRegistryId = config.registryIds.fileStatusRegistryId;
    const got = await gql.getSDOSchemaId(dataRegistryId);
    console.info('got =====>', got);
    expect(got).toBeDefined();
  }, 120000);

  it.skip('getTDOAssets', async () => {
    const tdoId = '3530031735';
    // const assetTypes = ['content-template'];
    const got = await gql.getTDOAssets({ tdoId });
    console.info(`got ${got.length} =====>`, got);
    expect(got).toBeDefined();
  }, 120000);

  it.skip('getTDOTasks', async () => {
    const tdoId = '3590025628';
    const got = await gql.getTDOTasks(tdoId);
    console.info('got =====>', got);
    expect(got).toBeDefined();
  }, 120000);

  it.skip('createTDOContentTemplate', async () => {
    const dataRegistryId = config.registryIds.fileStatusRegistryId;
    const schemaId = await gql.getSDOSchemaId(dataRegistryId);
    const tdoId = '3650020593';
    const data = { status: 'pending' };
    const got = await gql.createTDOContentTemplate({
      tdoId,
      schemaId,
      data,
    });
    console.info('got =====>', got);
    expect(got).toBeDefined();
  }, 120000);

  it.skip('getTDOContentTemplate', async () => {
    const dataRegistryId = config.registryIds.fileStatusRegistryId;
    const tdoId = '3650020593';
    const got = await gql.getTDOContentTemplate<{ status: string }>({
      tdoId,
      dataRegistryId,
    });
    console.info('got =====>', got);
    expect(got).toBeDefined();
  }, 120000);

  it.skip('updateTDOContentTemplate', async () => {
    const dataRegistryId = config.registryIds.fileStatusRegistryId;
    const tdoId = '3650010298';
    const data = { status: 'pending' };
    const got = await gql.updateTDOContentTemplate<{ status: string }>({
      tdoId,
      dataRegistryId,
      data,
    });
    console.info('got =====>', got);
    expect(got).not.toBeDefined();
  }, 120000);

  it.skip('deleteAssets', async () => {
    const assetIds = ['3650020593_vVnJSnPDSb'];
    const got = await gql.deleteAssets(assetIds);
    console.info('got =====>', got);
    expect(got).toBeDefined();
  }, 120000);

  it.skip('getTDOByOffset', async () => {
    const offset = 0;
    const limit = 10;
    const dateTimeFilter = {
      field: 'createdDateTime',
      fromDateTime: '2023-10-01T00:00:00Z',
    };

    const got = await gql.getTDOByOffset({
      offset,
      limit,
      dateTimeFilter,
    });
    console.info('got =====>', got);
    expect(got).toBeDefined();
  }, 120000);
});
