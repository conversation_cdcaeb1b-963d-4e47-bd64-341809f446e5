import { z } from 'zod';
const HIDE_IF_NO_RESULTS_KEY = 'investigate-hide-if-no-results';

const DEFAULT_HIDE_IF_NO_RESULTS = true;

const booleanSchema = z.boolean();
export const getHideIfNoResultsLocalStorage = (): boolean => {
  const storage = localStorage.getItem(HIDE_IF_NO_RESULTS_KEY);
  if (!storage) {
    return DEFAULT_HIDE_IF_NO_RESULTS;
  }
  try {
    const parsedData: unknown = JSON.parse(storage);
    const validationResult = booleanSchema.safeParse(parsedData);
    if (validationResult.success) {
      return validationResult.data;
    }
  } catch {
    return DEFAULT_HIDE_IF_NO_RESULTS;
  }
  return DEFAULT_HIDE_IF_NO_RESULTS;
};

export const setHideIfNoResultsLocalStorage = (value: boolean) => {
  localStorage.setItem(HIDE_IF_NO_RESULTS_KEY, JSON.stringify(value));
};
