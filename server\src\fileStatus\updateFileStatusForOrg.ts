import { GQLApi } from '../utils/gqlApi';
import { updateFileStatus } from './updateFileStatus';

export async function updateFileStatusForOrg({
  organizationId,
  dataRegistryId,
  catchupHour = 2,
  gqlApi,
}: {
  organizationId: string;
  dataRegistryId: string;
  catchupHour?: number;
  gqlApi: GQLApi;
}) {
  const now = new Date();
  now.setHours(now.getHours() - catchupHour);
  console.info(
    `investigate - catchup - org: ${organizationId} - catchupHour: ${catchupHour}, from: ${now.toISOString()}`
  );
  await updateFileStatusForOrgFromGivenTime({
    organizationId,
    dataRegistryId,
    gqlApi,
    fromDateTimeUTC: now.toISOString(),
  });
  console.info(
    `investigate - catchup - org: ${organizationId} - from: ${now.toISOString()} completed`
  );
}

export async function updateFileStatusForOrgFromGivenTime({
  organizationId,
  fromDateTimeUTC,
  dataRegistryId,
  gqlApi,
}: {
  organizationId: string;
  fromDateTimeUTC: string;
  dataRegistryId: string;
  gqlApi: GQLApi;
}) {
  let offset = 0;
  let count = 0;
  const limit = 10;
  const dateTimeFilter = {
    field: 'modifiedDateTime',
    fromDateTime: fromDateTimeUTC,
  };
  do {
    const tdoIds = await gqlApi.getTDOByOffset({
      offset,
      limit,
      dateTimeFilter,
    });
    offset += limit;
    count = tdoIds.length;
    const processingTdoIds = tdoIds.map(async (tdoId) => {
      return await updateFileStatus({
        tdoId,
        dataRegistryId,
        gqlApi,
        orgId: organizationId,
        logMsg: 'catchup',
      });
    });
    const result = await Promise.all(processingTdoIds);
    const failed = result.filter((res) => res.error).map((res) => res.tdoId);
    console.info(
      `investigate - catchup - org: ${organizationId} - updated ${result.length} tdos - failed: ${failed.length}`,
      failed
    );
  } while (count === limit);
}
