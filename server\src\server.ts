import express from 'express';
import bodyParser from 'body-parser';
import { loadApiConfig } from './utils/loadApiConfig';
import routes from './routes';
import path from 'path';

const configPath = path.join(__dirname, '../apiConfig.json');
const appConfig = loadApiConfig(configPath);
console.info('investigate backend apiConfig', appConfig);

let serviceToken;
if (process.env.SERVICE_TOKEN) {
  console.info('Loaded serviceToken from SERVICE_TOKEN environment variable');
  serviceToken = process.env.SERVICE_TOKEN;
} else if (appConfig.serviceToken) {
  console.info('Loaded serviceToken from appConfig.serviceToken');
  serviceToken = appConfig.serviceToken;
}
if (!serviceToken) {
  console.error(
    'No serviceToken found. Please set SERVICE_TOKEN environment variable or serviceToken in apiConfig.json'
  );
  throw new Error('No serviceToken found.');
}

const app = express();

app.locals.appConfig = appConfig;
app.locals.serviceToken = serviceToken;
// This map is used to track expiration times for organizations to avoid processing them too frequently
app.locals.orgExpirationMap = {};

app.use(bodyParser.json());
app.use('/api', routes);
app.use((_, res) => {
  res.status(404).json({ error: 'Resource not found.' });
});

const port = process.env.PORT || appConfig.port || 3000;
app.listen(port, () => {
  console.info(`Server is running on port ${port}`);
});
