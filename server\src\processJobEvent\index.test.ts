import { describe, expect, it } from 'vitest';
import config from '../../apiConfig.json';
import { processJobEvent } from '.';

describe('processJobEvent', () => {
  // not unit test. just a driver to help troubleshoot processJobEvent
  it.skip('process jobEvent', async () => {
    const jobId = '25062303_7qglq7Sqwp';
    const appConfig = config;
    const serviceToken = appConfig.serviceToken;
    const got = await processJobEvent(jobId, appConfig, serviceToken);
    console.info('got =====>', got);
    expect(got).toBeDefined();
  }, 500000);
});
