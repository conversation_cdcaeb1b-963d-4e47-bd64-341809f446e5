import './index.scss';
import { I18nTranslate } from '@i18n';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Remove';
import { Button, Checkbox, TextField } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { useAppDispatch } from '@store/hooks';
import { toggleShareDrawer } from '@store/modules/caseManager/slice';
import { useEffect, useState } from 'react';
import cn from 'classnames';

/* eslint-disable no-control-regex */
const emailValidationRegex =
  /(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/;

const mkKey = () => Math.floor(Math.random() * 10000);
interface ValidationErrors {
  shareName: boolean;
  message: boolean;
  expirationDate: boolean;
  expirationDateNotGreaterThanNow: boolean;
  recipients: {
    email: boolean;
    firstName: boolean;
    lastName: boolean;
    emailBadFormat: boolean;
  }[];
}

interface Props {
  caseId?: string;
  caseFilesCount?: number;
}

const Share = ({ caseId, caseFilesCount }: Props) => {
  const intl = I18nTranslate.Intl();
  const [recipients, setRecipients] = useState<
    {
      email: string;
      firstName: string;
      lastName: string;
      key: number;
    }[]
  >([
    {
      email: '',
      firstName: '',
      lastName: '',
      key: mkKey(),
    },
  ]);
  const [shareName, setShareName] = useState('');
  const [message, setMessage] = useState('');
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [password, setPassword] = useState<string>('');
  const [expirationDate, setExpirationDate] = useState<Date | null>(null);
  const [_validationErrors, setValidationErrors] = useState<ValidationErrors>({
    shareName: false,
    message: false,
    expirationDate: false,
    expirationDateNotGreaterThanNow: false,
    recipients: [],
  });
  const [formValid, setFormValid] = useState<boolean>(false);

  const dispatch = useAppDispatch();

  useEffect(() => {
    // Reset state when component mounts
    setRecipients([
      {
        email: '',
        firstName: '',
        lastName: '',
        key: mkKey(),
      },
    ]);
    setShareName('');
    setMessage('');
    setExpirationDate(null);
  }, []);

  // Validate fields
  const isFormValid = () => {
    const shareNameValid = shareName.trim() !== '';
    const messageValid = message.trim() !== '';
    const expirationDateValid = expirationDate !== null;
    const expirationDateIsGreaterThanNow =
      expirationDateValid && expirationDate > new Date();
    const recipientsValid = recipients.map((recipient) => ({
      email: recipient.email.trim() === '',
      emailBadFormat: !emailValidationRegex.test(recipient.email.trim()),
      firstName: recipient.firstName.trim() === '',
      lastName: recipient.lastName.trim() === '',
    }));

    setValidationErrors({
      shareName: !shareNameValid,
      message: !messageValid,
      expirationDate: !expirationDateValid,
      expirationDateNotGreaterThanNow: !expirationDateIsGreaterThanNow,
      recipients: recipientsValid,
    });

    const valid =
      shareNameValid &&
      messageValid &&
      expirationDateValid &&
      recipientsValid.every(
        (recipient) =>
          !recipient.email &&
          !recipient.firstName &&
          !recipient.lastName &&
          !recipient.emailBadFormat
      );
    setFormValid(valid);
    // Return true if all fields are valid
    return valid;
  };

  useEffect(() => {
    // Validate the form whenever any field changes
    isFormValid();
  }, [shareName, message, expirationDate, recipients, password]);

  const handleShare = () => {
    if (!isFormValid()) {
      return;
    }
    // Reset the form after sharing
    setShareName('');
    setMessage('');
    setPassword('');
    setExpirationDate(null);
    setRecipients([
      {
        email: '',
        firstName: '',
        lastName: '',
        key: mkKey(),
      },
    ]);
    // Close the drawer after sharing
    dispatch(toggleShareDrawer());
  };

  const handleCancel = () => {
    // Reset the form after canceling
    setShareName('');
    setMessage('');
    setPassword('');
    setExpirationDate(null);
    setRecipients([
      {
        email: '',
        firstName: '',
        lastName: '',
        key: mkKey(),
      },
    ]);
    dispatch(toggleShareDrawer());
  };

  const handleChangeCheckbox = () => {
    setShowPassword(!showPassword);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterLuxon}>
      <div className="share" data-testid="share">
        <h3 className="share__title">
          {I18nTranslate.TranslateMessage('createShare')}
        </h3>
        <div className="share__subtitle">
          {I18nTranslate.TranslateMessage('createShareSubtitle')}
        </div>
        <div className="share__case-meta">
          {caseId !== undefined && (
            <>
              <div className="share__case-meta-id-label">
                {I18nTranslate.TranslateMessage('caseId')}
                {I18nTranslate.TranslateMessage(':')}
              </div>
              <div className="share__case-meta-id">{caseId}</div>
            </>
          )}
          {caseFilesCount !== undefined && (
            <>
              <div className="share__case-meta-files-count-label">
                {I18nTranslate.TranslateMessage('caseFiles')}
                {I18nTranslate.TranslateMessage(':')}
              </div>
              <div className="share__case-meta-files-count">
                {caseFilesCount}
              </div>
            </>
          )}
        </div>
        <div className={'share__field-header'}>
          {I18nTranslate.TranslateMessage('shareName')}
        </div>
        <TextField
          className={'share__field-share-name'}
          variant="outlined"
          fullWidth
          data-testid="share-with-input"
          onChange={(e) => setShareName(e.target.value)}
          value={shareName}
          type="text"
          // error={validationErrors.shareName}
          // helperText={
          //   validationErrors.shareName
          //     ? I18nTranslate.TranslateMessage('shareNameRequired')
          //     : ''
          // }
        />
        <div className={'share__field-header'}>
          {I18nTranslate.TranslateMessage('message')}
        </div>
        <TextField
          variant="outlined"
          fullWidth
          multiline
          rows={4}
          data-testid="share-with-input"
          onChange={(e) => setMessage(e.target.value)}
          value={message}
          type="text"
          // error={validationErrors.message}
          // helperText={
          //   validationErrors.message
          //     ? I18nTranslate.TranslateMessage('messageRequired')
          //     : ''
          // }
        />
        <div className="share__wrapper-row">
          <div className="share__wrapper-expiration-date">
            <div className={'share__field-header'}>
              {I18nTranslate.TranslateMessage('expirationDate')}
            </div>
            <DatePicker
              className={'share__field-expiration-date'}
              value={expirationDate}
              onChange={setExpirationDate}
              slots={{
                textField: (params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    fullWidth
                    slotProps={{
                      htmlInput: {
                        'data-testid': 'share-expiration-date',
                      },
                    }}
                    // error={
                    //   validationErrors.expirationDate ||
                    //   validationErrors.expirationDateNotGreaterThanNow
                    // }
                    // helperText={
                    //   validationErrors.expirationDate
                    //     ? I18nTranslate.TranslateMessage(
                    //       'expirationDateRequired'
                    //     )
                    //     : validationErrors.expirationDateNotGreaterThanNow
                    //       ? I18nTranslate.TranslateMessage(
                    //         'expirationDateNotGreaterThanNow'
                    //       )
                    //       : ''
                    // }
                  />
                ),
              }}
            />
          </div>
          <div className="share__wrapper-password">
            <div className="share__wrapper-field-password">
              <div className={'share__field-header'}>
                {I18nTranslate.TranslateMessage('password')}
              </div>
              <TextField
                className={'share__field-password'}
                variant="outlined"
                fullWidth
                slotProps={{
                  htmlInput: {
                    'data-testid': 'share-password-input',
                  },
                }}
                onChange={(e) => setPassword(e.target.value)}
                value={password}
                type={showPassword ? 'text' : 'password'}
              />
            </div>
            <div className="share__show-password-option">
              <Checkbox onChange={handleChangeCheckbox} />
              <p className="share__show-password-text">
                {I18nTranslate.TranslateMessage('showPassword')}
              </p>
            </div>
          </div>
        </div>
        <div className={'share__field-header'}>
          {I18nTranslate.TranslateMessage('recipients')}
        </div>

        {recipients.map((recipient, index) => (
          <div
            className="share__recipient"
            data-testid={`share-recipient-${index}`}
            key={`recipient-${recipient.key}`}
          >
            <TextField
              className="share__recipient-email"
              variant="outlined"
              placeholder={intl.formatMessage({ id: 'emailAddresses' })}
              onChange={(e) => {
                const newRecipients = [...recipients];
                newRecipients[index].email = e.target.value;
                setRecipients(newRecipients);
              }}
              value={recipient.email}
              type="email"
              required
              autoComplete="email"
              slotProps={{
                htmlInput: {
                  'data-testid': `share-input-email-${index}`,
                },
              }}
              // error={
              //   validationErrors.recipients[index]?.email ||
              //   validationErrors.recipients[index]?.emailBadFormat
              // }
              // helperText={
              //   validationErrors.recipients[index]?.email
              //     ? I18nTranslate.TranslateMessage('emailRequired')
              //     : validationErrors.recipients[index]?.emailBadFormat
              //       ? I18nTranslate.TranslateMessage('emailBadFormat')
              //       : ''
              // }
            />
            <TextField
              className="share__recipient-first-name"
              variant="outlined"
              placeholder={intl.formatMessage({ id: 'firstName' })}
              onChange={(e) => {
                const newRecipients = [...recipients];
                newRecipients[index].firstName = e.target.value;
                setRecipients(newRecipients);
              }}
              value={recipient.firstName}
              type="text"
              required
              autoComplete="given-name"
              slotProps={{
                htmlInput: {
                  'data-testid': 'share-with-first-name-input',
                },
              }}
              // error={validationErrors.recipients[index]?.firstName}
              // helperText={
              //   validationErrors.recipients[index]?.firstName
              //     ? I18nTranslate.TranslateMessage('firstNameRequired')
              //     : ''
              // }
            />
            <TextField
              className="share__recipient-last-name"
              variant="outlined"
              placeholder={intl.formatMessage({ id: 'lastName' })}
              onChange={(e) => {
                const newRecipients = [...recipients];
                newRecipients[index].lastName = e.target.value;
                setRecipients(newRecipients);
              }}
              value={recipient.lastName}
              type="text"
              required
              autoComplete="family-name"
              // error={validationErrors.recipients[index]?.lastName}
              // helperText={
              //   validationErrors.recipients[index]?.lastName
              //     ? I18nTranslate.TranslateMessage('lastNameRequired')
              //     : ''
              // }
            />
            {recipients.length > 1 && (
              <DeleteIcon
                className="share__recipient-delete"
                onClick={() => {
                  const newRecipients = [...recipients];
                  newRecipients.splice(index, 1);
                  setRecipients(newRecipients);
                }}
                data-testid={`delete-recipient-${index}`}
              />
            )}
          </div>
        ))}
        <Button
          color="primary"
          className="share__add-recipient"
          data-testid="share-add-recipient-button"
          onClick={() => {
            setRecipients([
              ...recipients,
              {
                email: '',
                firstName: '',
                lastName: '',
                key: mkKey(),
              },
            ]);
          }}
        >
          <AddIcon />
          {I18nTranslate.TranslateMessage('addRecipient')}
        </Button>
        <div className={'share__footer'}>
          <div className={'share__footer-message'}>
            {I18nTranslate.TranslateMessage(
              'youAreAboutToShareSensitiveContent'
            )}
          </div>
          <div className={'share__footer-buttons'}>
            <Button
              onClick={handleCancel}
              color="inherit"
              className="share__footer-button-cancel"
              data-testid="share-cancel-button"
            >
              {I18nTranslate.TranslateMessage('cancel')}
            </Button>
            <Button
              className={cn('share__footer-button-share', {
                disabled: !formValid,
              })}
              variant="contained"
              color="primary"
              onClick={handleShare}
              style={{ textTransform: 'none' }}
              data-testid="share-save-button"
              disabled={!formValid}
            >
              {I18nTranslate.TranslateMessage('share')}
            </Button>
          </div>
        </div>
      </div>
    </LocalizationProvider>
  );
};

export default Share;
