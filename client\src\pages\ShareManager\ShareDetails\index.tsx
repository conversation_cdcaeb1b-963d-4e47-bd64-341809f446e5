import './index.scss';
import { CalendarIcon } from '@assets/icons';
import AddUserRow from '@components/Share/AddUserRow';
import ShareStatus from '@components/Share/ShareStatus';
import { I18nTranslate } from '@i18n';
import { MoreVert as MoreVertIcon } from '@mui/icons-material';
import AddIcon from '@mui/icons-material/Add';
import {
  Button,
  IconButton,
  Menu,
  MenuItem,
  Paper,
  Popover,
  PopoverPosition,
} from '@mui/material';
import { PickersActionBar } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { PickersToolbar } from '@mui/x-date-pickers/internals';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker';
import { useState } from 'react';
import { ShareDetailsData } from './mockData';

interface Props {
  shareId: string;
  data: ShareDetailsData;
}

const ShareDetails = ({ shareId, data }: Props) => {
  const intl = I18nTranslate.Intl();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuPosition, setMenuPosition] = useState<PopoverPosition | undefined>(
    undefined
  );
  const [calendarAnchorEl, setCalendarAnchorEl] = useState<null | HTMLElement>(
    null
  );

  const [isAddingUser, setIsAddingUser] = useState<boolean>(false);
  const [newUserInfo, setNewUserInfo] = useState({
    firstName: '',
    lastName: '',
    email: '',
  });

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuPosition(undefined);
  };

  const handleCalendarOpen = (event: React.MouseEvent<HTMLElement>) => {
    setCalendarAnchorEl(event.currentTarget);
  };

  const handleCalendarClose = () => {
    setCalendarAnchorEl(null);
  };

  const handleAddUserClick = () => {
    setIsAddingUser(true);
  };

  const handleCancelAddUser = () => {
    setIsAddingUser(false);
    setNewUserInfo({ firstName: '', lastName: '', email: '' });
  };

  const handleNewUserInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewUserInfo((prev) => ({ ...prev, [name]: value }));
  };

  const handleSendInvite = () => {
    handleCancelAddUser();
  };

  const isSendButtonDisabled =
    !newUserInfo.firstName || !newUserInfo.lastName || !newUserInfo.email;

  return (
    <Paper
      className="share-details"
      style={{ display: shareId ? 'block' : 'none' }}
      elevation={0}
    >
      <div className="share-details__body" data-testid="share-details-content">
        <Button
          className="share-details__view-btn"
          data-testid="view-share-details-button"
        >
          {I18nTranslate.TranslateMessage('viewShareDetails')}
        </Button>

        <div className="share-details__title">
          <h1 className="share-details__title-text">{data.shareName}</h1>
        </div>

        <div className="share-details__status">
          <h3 className="share-details__status-text">
            {I18nTranslate.TranslateMessage('share')}
          </h3>
          <ShareStatus status={data.status} />
        </div>

        <div className="share-details__access">
          <div className="share-details__access-header">
            <div className="share-details__access-header-info">
              <h3 className="share-details__access-header-title">
                {I18nTranslate.TranslateMessage('access')}
              </h3>
              <h3 className="share-details__access-header-subtitle">
                {I18nTranslate.TranslateMessage('userGrantedAccess')}
              </h3>
            </div>
            <div className="share-details__access-header-action">
              <Button
                disabled={isAddingUser}
                onClick={handleAddUserClick}
                className="share-details__access-header-btn"
                startIcon={
                  <AddIcon
                    sx={{ height: '18px', width: '18px', cursor: 'pointer' }}
                  />
                }
              >
                {I18nTranslate.TranslateMessage('addUser')}
              </Button>
            </div>
          </div>

          <div className="share-details__access-list">
            {data.accessList.map((user) => (
              <div key={user.email} className="share-details__access-list-item">
                <p className="share-details__access-list-item-name">
                  {user.name}
                </p>
                <p className="share-details__access-list-item-email">
                  {user.email}
                </p>
                {user.status && (
                  <h3 className="share-details__access-list-item-status">
                    {user.status}
                  </h3>
                )}
                <IconButton
                  size="small"
                  onClick={handleMenuOpen}
                  data-testid={`access-list-item-menu-${user.email}`}
                  className="share-details__access-list-item-moreIcon"
                >
                  <MoreVertIcon />
                </IconButton>
              </div>
            ))}
            {isAddingUser && (
              <AddUserRow
                userInfo={newUserInfo}
                onInputChange={handleNewUserInputChange}
                onSend={handleSendInvite}
                onCancel={handleCancelAddUser}
                isSendDisabled={isSendButtonDisabled}
              />
            )}
          </div>
        </div>

        <div className="share-details__additional-info">
          <h1 className="share-details__additional-info-title">
            {I18nTranslate.TranslateMessage('additionalInformation')}
          </h1>
          <div className="share-details__additional-info-list">
            <div className="share-details__additional-info-item">
              <p className="share-details__additional-info-item-label">
                {I18nTranslate.TranslateMessage('sharedBy')}
              </p>
              <p className="share-details__additional-info-item-value">
                {data.additionalInfo.sharedBy}
              </p>
            </div>
            <div className="share-details__additional-info-item">
              <p className="share-details__additional-info-item-label">
                {I18nTranslate.TranslateMessage('expirationDateWithColons')}
              </p>
              <p className="share-details__additional-info-item-value">
                {data.additionalInfo.expirationDate}
              </p>
              <IconButton
                className="share-details__additional-info-item-icon"
                data-testid="calendar-icon-button"
                onClick={handleCalendarOpen}
              >
                <CalendarIcon />
              </IconButton>
            </div>
            <div className="share-details__additional-info-item">
              <p className="share-details__additional-info-item-label">
                {I18nTranslate.TranslateMessage('sharedDateWithColons')}
              </p>
              <p className="share-details__additional-info-item-value">
                {data.additionalInfo.sharedDate}
              </p>
            </div>
            <div className="share-details__additional-info-item">
              <p className="share-details__additional-info-item-label">
                {I18nTranslate.TranslateMessage('caseIdWithColons')}
              </p>
              <p
                className="share-details__additional-info-item-value"
                style={{ color: '#1565C0' }}
              >
                {data.additionalInfo.caseId}
              </p>
            </div>
            <div className="share-details__additional-info-item">
              <p className="share-details__additional-info-item-label">
                {I18nTranslate.TranslateMessage('numberOfFiles')}
              </p>
              <p className="share-details__additional-info-item-value">
                {data.additionalInfo.numberOfFiles}
              </p>
            </div>
          </div>
        </div>
      </div>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
        anchorReference={menuPosition ? 'anchorPosition' : 'anchorEl'}
        anchorPosition={menuPosition}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            className: 'share-details__menu-paper',
          },
        }}
      >
        <MenuItem onClick={handleMenuClose}>
          {I18nTranslate.TranslateMessage('resendShare')}
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          {I18nTranslate.TranslateMessage('removeUser')}
        </MenuItem>
      </Menu>

      <Popover
        open={Boolean(calendarAnchorEl)}
        anchorEl={calendarAnchorEl}
        onClose={handleCalendarClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <LocalizationProvider
          dateAdapter={AdapterDayjs}
          localeText={{
            cancelButtonLabel: intl.formatMessage({ id: 'cancel' }),
            okButtonLabel: intl.formatMessage({ id: 'save' }),
          }}
        >
          <StaticDatePicker
            slots={{
              actionBar: PickersActionBar,
              toolbar: (props) => (
                <PickersToolbar toolbarTitle={undefined} {...props}>
                  <div style={{ padding: '8px', fontWeight: 'bold' }}>
                    {I18nTranslate.TranslateMessage(
                      'changeShareExpirationDate'
                    )}
                  </div>
                </PickersToolbar>
              ),
            }}
            slotProps={{
              actionBar: {
                actions: ['cancel', 'accept'],
              },
            }}
          />
        </LocalizationProvider>
      </Popover>
    </Paper>
  );
};
export default ShareDetails;
