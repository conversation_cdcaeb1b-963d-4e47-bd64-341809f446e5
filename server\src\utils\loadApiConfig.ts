import fs from 'fs';
import { AppConfig } from '../types';

export function loadApiConfig(path: string): AppConfig {
  try {
    const configFile = fs.readFileSync(path, 'utf8');
    const config = JSON.parse(configFile);
    validateConfig(config);
    return config;
  } catch (error) {
    console.error(`Failed to read or parse the configuration file: ${error}`);
    throw new Error(
      `Failed to load apiConfig.json at ${path}, error: ${error}`
    );
  }
}

function validateConfig(config: AppConfig): void {
  if (!config.apiRoot) {
    throw new Error('Invalid configuration: Missing apiRoot');
  }
  if (!config.graphQLEndpoint) {
    throw new Error('Invalid configuration: Missing graphQLEndpoint');
  }
  if (!config.veritoneAppId) {
    throw new Error('Invalid configuration: Missing veritoneAppId');
  }
  if (!config.registryIds.fileStatusRegistryId) {
    throw new Error('Invalid configuration: Missing fileStatusRegistryId');
  }
  if (!config.investigateUserRoleId) {
    throw new Error('Invalid configuration: Missing investigateUserRoleId');
  }
}
