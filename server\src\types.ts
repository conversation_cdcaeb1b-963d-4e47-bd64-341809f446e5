export interface Asset {
  id: string;
  transform?: string;
  sourceData?: {
    schemaId?: string;
  };
}

export interface Task {
  id: string;
  status: string;
  modifiedDateTime: string;
  engine?: {
    id: string;
    name: string;
  };
}

export type fileStatus = 'pending' | 'processed' | 'error';

export interface JobEvent {
  jobId: string;
  timestampMs?: string;
  jobStatus?: string;
  vtn?: {
    app: string;
    eventName: string;
    type: string;
  };
}

export interface AppConfig {
  apiRoot: string;
  graphQLEndpoint: string;
  veritoneAppId: string;
  registryIds: {
    fileStatusRegistryId: string;
  };
  investigateUserRoleId: string;
  catchupHour?: number;
  serviceToken?: string;
  port?: number;
}
