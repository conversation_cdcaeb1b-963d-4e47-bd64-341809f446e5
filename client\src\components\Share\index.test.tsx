import Drawer from '../Drawer/index.tsx';
import { render } from '../../../test/render.tsx';
import Share from './index.tsx';
import { screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('Create Case', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the Share Drawer', () => {
    const { container } = render(
      <Drawer
        open
        onClose={() => {}}
        width={750}
        anchor={'right'}
        persistent={false}
      >
        <Share caseFilesCount={2323} caseId="12345test" />
      </Drawer>
    );

    expect(container).toBeTruthy();
    expect(screen.getByText('Create Share')).toBeTruthy();
    expect(screen.getByText('Case ID:')).toBeTruthy();
    expect(screen.getByText('2323')).toBeTruthy();
    expect(screen.getByText('Case Files:')).toBeTruthy();
    expect(screen.getByText('12345test')).toBeTruthy();
  });

  // it('show validation errors when fields are empty', async () => {
  //   const { container } = render(
  //     <Drawer
  //       open
  //       onClose={() => {}}
  //       width={750}
  //       anchor={'right'}
  //       persistent={false}
  //     >
  //       <Share />
  //     </Drawer>
  //   );

  //   expect(container).toBeTruthy();

  //   screen.getByTestId('share-save-button').click();

  //   await waitFor(() => {
  //     expect(screen.getByText('Share Name is required')).toBeTruthy();
  //     expect(screen.getByText('Message is required')).toBeTruthy();
  //     expect(screen.getByText('Expiration Date is required')).toBeTruthy();
  //     expect(screen.getByText('Email is required')).toBeTruthy();
  //     expect(screen.getByText('First Name is required')).toBeTruthy();
  //     expect(screen.getByText('Last Name is required')).toBeTruthy();
  //   });
  // });

  // it('show validation errors when email is invalid', async () => {
  //   const { container } = render(
  //     <Drawer
  //       open
  //       onClose={() => {}}
  //       width={750}
  //       anchor={'right'}
  //       persistent={false}
  //     >
  //       <Share />
  //     </Drawer>
  //   );

  //   expect(container).toBeTruthy();

  //   fireEvent.change(screen.getByTestId('share-input-email-0'), {
  //     target: { value: 'invalid-email' },
  //   });

  //   screen.getByTestId('share-save-button').click();

  //   await waitFor(() => {
  //     expect(screen.getByText('Email is not a valid format')).toBeTruthy();
  //   });
  // });

  it('adds recipients when add is clicked', async () => {
    const { container } = render(
      <Drawer
        open
        onClose={() => {}}
        width={750}
        anchor={'right'}
        persistent={false}
      >
        <Share />
      </Drawer>
    );

    expect(container).toBeTruthy();

    screen.getByTestId('share-add-recipient-button').click();

    await waitFor(() => {
      expect(screen.getByTestId('share-recipient-0')).toBeInTheDocument();
      expect(screen.getByTestId('share-recipient-1')).toBeInTheDocument();
    });
  });
});
