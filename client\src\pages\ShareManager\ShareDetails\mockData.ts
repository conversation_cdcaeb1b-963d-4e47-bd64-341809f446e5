export interface ShareUser {
  name: string;
  email: string;
  status?: string;
}

export interface ShareDetailsData {
  shareName: string;
  status: 'ACTIVE' | 'EXPIRED';
  accessList: ShareUser[];
  additionalInfo: {
    sharedBy: string;
    expirationDate: string;
    sharedDate: string;
    caseId: string;
    numberOfFiles: number;
  };
}

export const mockShareData: ShareDetailsData = {
  shareName: 'Case #45662990 Evidence Package',
  status: 'ACTIVE',
  accessList: [
    {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'Share Pending',
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    {
      name: '<PERSON>',
      email: 'b<PERSON><PERSON><PERSON>@veritone.com',
    },
  ],
  additionalInfo: {
    sharedBy: '<PERSON>',
    expirationDate: '08/17/2025',
    sharedDate: '07/17/2025',
    caseId: '45662990',
    numberOfFiles: 15,
  },
};
