import { render, screen } from '@testing-library/react';
import ShareDetails from './index';
import { ShareDetailsData } from './mockData';
import { describe, expect, it, vi } from 'vitest';

const mockData: ShareDetailsData = {
  shareName: 'Case #12345 Test Package',
  status: 'ACTIVE',
  accessList: [
    { name: 'User One', email: '<EMAIL>', status: 'Active' },
    { name: 'User Two', email: '<EMAIL>', status: 'Expired' },
  ],
  additionalInfo: {
    caseId: '12345',
    sharedBy: 'Admin',
    expirationDate: '2025-12-31',
    sharedDate: '2025-07-23',
    numberOfFiles: 10,
  },
};

vi.mock('@i18n', () => ({
  I18nTranslate: {
    TranslateMessage: (id: string) => id,
    Intl: () => ({
      formatMessage: ({ id }: { id: string }) => id,
    }),
  },
}));

describe('ShareDetails', () => {
  it('renders the correct case title', () => {
    render(<ShareDetails shareId="shareId" data={mockData} />);
    expect(screen.getByText(/Case #12345 Test Package/)).toBeInTheDocument();
  });

  it('renders the access list with correct statuses', () => {
    render(<ShareDetails shareId="shareId" data={mockData} />);

    expect(screen.getByText('User One')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();

    expect(screen.getByText('User Two')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();

    expect(screen.getByText('Expired')).toBeInTheDocument();

    const activeStatuses = screen.getAllByText(/active/i);
    expect(activeStatuses.length).toBeGreaterThanOrEqual(2);
  });

  it('renders additional info fields', () => {
    render(<ShareDetails shareId="shareId" data={mockData} />);
    expect(screen.getByText(/sharedBy/i)).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
    expect(screen.getByText(/expirationDateWithColons/i)).toBeInTheDocument();
    expect(screen.getByText('2025-12-31')).toBeInTheDocument();
    expect(screen.getByText(/sharedDateWithColons/i)).toBeInTheDocument();
    expect(screen.getByText('2025-07-23')).toBeInTheDocument();
    expect(screen.getByText(/caseIdWithColons/i)).toBeInTheDocument();
    expect(screen.getAllByText('12345').length).toBeGreaterThan(0);
    expect(screen.getByText(/numberOfFiles/i)).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
  });

  it('renders the calendar icon button', () => {
    render(<ShareDetails shareId="shareId" data={mockData} />);
    expect(screen.getByTestId('calendar-icon-button')).toBeInTheDocument();
  });

  it('renders the add user button', () => {
    render(<ShareDetails shareId="shareId" data={mockData} />);
    expect(screen.getByText(/addUser/i)).toBeInTheDocument();
  });
});
