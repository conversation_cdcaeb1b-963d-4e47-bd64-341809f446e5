import './index.scss';
import {
  Dashboard,
  FolderClosed,
  FolderOpen,
  Search,
  Settings,
  Share,
} from '@assets/icons';
import Menu from '@components/Menu';
import { I18nTranslate } from '@i18n';
import {
  Add as AddIcon,
  BedtimeOutlined as BedtimeOutlinedIcon,
  DescriptionOutlined as DescriptionOutlinedIcon,
  LightMode as LightModeIcon,
  WorkOutlineOutlined as WorkOutlineOutlinedIcon,
} from '@mui/icons-material';
import { Box, Button, MenuItem, Tooltip } from '@mui/material';
import { useAppDispatch } from '@store/hooks';
import { syncPendingFiles } from '@store/modules/caseDetail/slice';
import {
  fileUploadedSuccessfully,
  selectCaseData,
  toggleCaseDrawer,
  selectAllFolderIds,
} from '@store/modules/caseManager/slice';
import { selectConfig } from '@store/modules/config/slice';
import { switchCurrentTheme, useToggleTheme } from '@theme';
import cn from 'classnames';
import { useSnackbar } from 'notistack';
import { MouseEvent, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router';
import { uploadFile } from '@utils/files/uploadFile';

const navList = (featureFlags?: { shareManager: boolean }) => [
  {
    id: 'dashboard',
    label: 'dashboard',
    testLabel: 'dashboard',
    iconOpen: <Dashboard />,
    iconClosed: <Dashboard />,
    path: '/dashboard',
    disabled: true,
  },
  {
    id: 'search',
    label: 'search',
    testLabel: 'search',
    iconOpen: <Search />,
    iconClosed: <Search />,
    path: '/search',
  },
  {
    id: 'case-manager',
    label: 'caseManager',
    testLabel: 'case-manager',
    iconOpen: <FolderOpen />,
    iconClosed: <FolderClosed />,
    path: '/case-manager',
  },
  {
    id: 'share',
    label: 'share',
    testLabel: 'share',
    iconOpen: <Share />,
    iconClosed: <Share />,
    path: '/share-manager',
    disabled: !featureFlags?.shareManager,
  },
  {
    id: 'settings',
    label: 'settings',
    testLabel: 'settings',
    iconOpen: <Settings />,
    iconClosed: <Settings />,
    path: '/settings',
  },
];

const NavBar = () => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const toggleTheme = useToggleTheme();
  const { enqueueSnackbar } = useSnackbar();
  const selectedCase = useSelector(selectCaseData);
  const { featureFlags, registryIds } = useSelector(selectConfig);
  const allFolderIds = useSelector(selectAllFolderIds);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleAddMenu = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => setAnchorEl(null);

  const handleCreateNewCase = () => {
    dispatch(toggleCaseDrawer());
    handleClose();
  };

  const handleUploadFile = () => {
    if (!allFolderIds || allFolderIds.length === 0) {
      enqueueSnackbar(intl.formatMessage({ id: 'noCasesPleaseCreateACase' }), {
        variant: 'error',
      });
      return;
    }
    uploadFile({
      orgFolderIds: allFolderIds,
      selectedFolderId: selectedCase.folderId,
      selectedCaseId: selectedCase.caseId,
      registryId: registryIds?.caseRegistryId,
      intl,
    });

    handleClose();
  };

  // const handleCreateNewShare = () => {
  //   handleClose();
  // };

  useEffect(() => {
    if (window.aiware) {
      window.aiware.on('tdoCreated', (error, data) => {
        if (error) {
          enqueueSnackbar(intl.formatMessage({ id: 'unableToUploadFile' }), {
            variant: 'error',
          });
        } else {
          const tdoData = data as { tdoId: string };
          dispatch(syncPendingFiles({ tdoId: tdoData.tdoId }));
          dispatch(fileUploadedSuccessfully({ tdoId: tdoData.tdoId }));
        }
      });
    }

    return () => {
      window.aiware?.off?.('tdoCreated');
    };
  }, [window.aiware]);

  return (
    <>
      <Box
        gap={1}
        className="tab-panel"
        flexDirection="column"
        sx={{ display: 'flex' }}
      >
        <Tooltip
          placement="right"
          title={I18nTranslate.TranslateMessage('add')}
        >
          <Button className="add-button" onClick={handleAddMenu}>
            <AddIcon />
          </Button>
        </Tooltip>
        {navList(featureFlags)
          .filter(({ disabled }) => !disabled)
          .map((tab) => {
            const selected = pathname.includes(tab.path);
            return (
              <Tooltip
                key={tab.id}
                placement="right"
                data-test={`nav-tab-${tab.testLabel}`}
                title={intl.formatMessage({ id: tab.label })}
              >
                <Button
                  onClick={() => {
                    if (!selected) {
                      navigate(tab.path);
                    }
                    return;
                  }}
                  className={cn('tab-button', { selected })}
                >
                  {selected ? tab.iconOpen : tab.iconClosed}
                </Button>
              </Tooltip>
            );
          })}
        <Tooltip
          placement="right"
          title={
            toggleTheme.isDark
              ? intl.formatMessage({ id: 'lightMode' })
              : intl.formatMessage({ id: 'darkMode' })
          }
        >
          <Button
            className="tab-button"
            style={{ marginTop: 'auto' }}
            onClick={switchCurrentTheme(toggleTheme)}
          >
            {toggleTheme.isDark ? <LightModeIcon /> : <BedtimeOutlinedIcon />}
          </Button>
        </Tooltip>
      </Box>
      <Menu
        id="nav-bar-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
      >
        <MenuItem
          data-testid="create-new-case-btn"
          onClick={handleCreateNewCase}
          disableRipple
        >
          <WorkOutlineOutlinedIcon />
          {I18nTranslate.TranslateMessage('createNewCase')}
        </MenuItem>
        <MenuItem
          data-testid="upload-files-btn"
          onClick={handleUploadFile}
          disableRipple
        >
          <DescriptionOutlinedIcon />
          {I18nTranslate.TranslateMessage('uploadFile(s)')}
        </MenuItem>
        {/* <MenuItem onClick={handleCreateNewShare} disableRipple> */}
        {/*  <ShareOutlinedIcon /> */}
        {/*  {I18nTranslate.TranslateMessage('createNewShare')} */}
        {/* </MenuItem> */}
      </Menu>
    </>
  );
};

export default NavBar;
