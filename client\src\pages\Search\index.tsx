import './index.scss';
import Filter from '@components/FilterPanel';
import SearchContents from '@components/Search/SearchContents';
import SearchControl from '@components/Search/SearchControl';
import { I18nTranslate } from '@i18n';
import { selectSelectedResults } from '@store/modules/search/slice';
import { updatePendingDeleteFileToLocalStorage } from '@utils/saveToLocalStorage';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

const Search = () => {
  const intl = I18nTranslate.Intl();
  const [pendingDeleteIds, setPendingDeleteIds] = useState<string[]>([]);
  const selectedResults = useSelector(selectSelectedResults);

  useEffect(() => {
    try {
      const validDeleteFileTdoIds = updatePendingDeleteFileToLocalStorage();
      setPendingDeleteIds(validDeleteFileTdoIds.map((item) => item.value));
    } catch (e) {
      console.error('Unable to update deleted file tdoIds to local storage', e);
    }
  }, [selectedResults]);

  return (
    <div className="search-page">
      <div className="search-page__header">
        <div className="search-page__header-title">
          <span>{intl.formatMessage({ id: 'searchPage' })}</span>
          {!!selectedResults.length && (
            <span className="search-page__header__file-count">
              <strong>{selectedResults.length}</strong>
              {intl.formatMessage({ id: 'space' })}
              {selectedResults.length === 1
                ? intl.formatMessage({ id: 'file' })
                : intl.formatMessage({ id: 'files' })}
              {intl.formatMessage({ id: 'space' })}
              {intl.formatMessage({ id: 'selected' })}
            </span>
          )}
        </div>
        <SearchControl setPendingDeleteIds={setPendingDeleteIds} />
      </div>
      <div className="search-page__content">
        <Filter />
        <SearchContents
          pendingDeleteIds={pendingDeleteIds}
          setPendingDeleteIds={setPendingDeleteIds}
        />
      </div>
    </div>
  );
};

export default Search;
