import { sortBy } from 'lodash';
import { fileStatus, Task } from '../types';
import { GQLApi } from '../utils/gqlApi';

export async function updateFileStatus({
  tdoId,
  dataRegistryId,
  gqlApi,
  orgId,
  logMsg = '',
}: {
  tdoId: string;
  dataRegistryId: string;
  gqlApi: GQLApi;
  orgId?: string;
  logMsg?: string;
}) {
  console.info(
    `investigate - updateFileStatus - updating - org ${orgId}, tdo: ${tdoId}, ${logMsg}`
  );
  let result;
  let status;
  try {
    const tasks = await gqlApi.getTDOTasks(tdoId);
    status = pickStatus(tasks);
    const contentTemplate = await gqlApi.getTDOContentTemplate<{
      status: string;
    }>({ tdoId, dataRegistryId });
    if (contentTemplate?.status === status) {
      result = { tdoId, status, updateResult: 'skip' };
      console.info(
        `investigate - updateFileStatus - same status`,
        result,
        logMsg
      );
      return result;
    }
    const newContentTemplate = { status };
    await gqlApi.updateTDOContentTemplate({
      tdoId,
      dataRegistryId,
      data: newContentTemplate,
    });
    result = { tdoId, status, updateResult: 'success' };
    console.info(`investigate - updateFileStatus - success`, result, logMsg);
  } catch (error) {
    result = { tdoId, status, updateResult: 'failed', error };
    console.error(
      `investigate - updateFileStatus - error`,
      result,
      logMsg,
      error
    );
  }
  return result;
}

export function pickStatus(tasks: Task[]): fileStatus {
  const sortedTasks = sortBy(tasks, 'modifiedDateTime');
  const taskStatusByEngine = {} as Record<string, string>;
  for (const task of sortedTasks) {
    const engineId = task.engine?.id;
    if (engineId) {
      taskStatusByEngine[engineId] = task.status;
    }
  }
  const statuses = Object.values(taskStatusByEngine);
  if (
    statuses.some((s) => s === 'failed' || s === 'aborted' || s === 'cancelled')
  ) {
    return 'error';
  }
  if (statuses.some((s) => s !== 'complete')) {
    return 'pending';
  }
  return 'processed';
}
