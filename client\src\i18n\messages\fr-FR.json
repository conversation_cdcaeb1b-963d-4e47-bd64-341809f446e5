{">": ">", "active": "Actif", "add": "Ajouter", "addFiles": "Ajouter des fichiers", "addNewCase": "<PERSON><PERSON>er un nouveau cas", "addNewStatus": "Ajouter un nouveau statut", "addNewTag": "Ajouter une nouvelle balise", "addStatus": "Ajouter un statut", "addTag": "Ajouter une balise", "addTags": "Ajouter des balises", "addToShare": "Ajouter au partage", "advancedSearch": "Recherche avancée", "areYouSure": "Êtes-vous sûr?!", "areYouSureCloseAndLoseData": "Êtes-vous sûr de vouloir fermer le panneau et perdre vos données ?", "back": "Retour", "backToCaseManager": "Retour au gestionnaire de cas", "belowYouCanFindKeySettings": "Vous trouverez ci-dessous les paramètres clés qui peuvent être gérés pour votre application.", "cancel": "Annuler", "case": "Cas", "caseFiles": "Fichiers du cas", "caseCreatedSuccessfully": "Cas créé avec succès !", "caseCreationFailed": "Échec de la création du cas", "caseDate": "Date du cas", "caseDateWithColons": "Date du cas :", "caseID": "ID du cas", "caseIDWithAsterisk": "ID du cas *", "caseIdWithColons": "ID du cas : ", "caseInformation": "Informations sur le cas", "caseManagement": "Gestion des cas", "caseManager": "Gestionnaire de cas", "caseName": "Nom du cas", "caseNameWithAsterisk": "Nom du cas *", "caseOwner": "Propriétaire du cas", "caseOwnerWithColons": "Propriétaire du cas :", "caseRetentionPolicy": "Politique de conservation du cas", "caseStatus": "Statut du cas", "chooseACase": "Choisir un cas", "clearAll": "Tout effacer", "clearStatus": "Effacer uniquement le statut", "close": "<PERSON><PERSON><PERSON>", "closePanel": "<PERSON><PERSON><PERSON> le pannea<PERSON>", "cognition": "Cognition", "confirmClosePanel": "Confirmer la fermeture du panneau", "createCase": "<PERSON><PERSON>er un cas", "createNewCase": "<PERSON><PERSON>er un nouveau cas", "createNewShare": "<PERSON><PERSON>er un nouveau partage", "confirmByTyping": "Confirmer en tapant", "below": "ci-dessous.", "deleteFilesConfirmationMsg": "La suppression supprimera immédiatement le contenu de votre organisation. Ils peuvent être récupérés pendant une période de 30 jours.", "deleteFile": "<PERSON><PERSON><PERSON><PERSON> le fichier", "everyOneInOrgWillLoseAccess": "Tout le monde dans l'organisation perdra l'accès", "theFileWillBeDeleted": "Le fichier sera supprimé", "allSelectedFilesWillBeDeleted": "Tout le contenu sélectionné sera supprimé", "dashboard": "Tableau de bord", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteAndReAssign": "Réattribuer et supprimer", "deleteAndReAssignStatusDescription": "Il y a des cas utilisant cette étiquette, effacez le statut{br} ou choisissez un nouveau statut pour les réattribuer.", "deleteCaseConfirmationMsg": "La suppression d'un cas supprime tous les fichiers et données et ne peut pas être annulée", "deleteOnlyStatusDescription": "Il y a des cas utilisant cette étiquette, choisissez un nouveau{br} statut pour les réattribuer.", "deleteStatus": "Supprimer l'étiquette de statut", "deleteStatusDescription": "Êtes-vous sûr de vouloir supprimer cette{br} étiquette de statut ? Cette action est irréversible.", "deleteTag": "Supprimer la balise", "deleteTagDescription": "Êtes-vous sûr de vouloir supprimer la balise {tag} ?", "deleteTags": "Supprimer les balises", "deleteTagsDescription": "Êtes-vous sûr de vouloir supprimer les balises sélectionnées ?", "description": "Description", "editCase": "Modifier le cas", "editRetention": "Modifier la rétention", "editStatusLabels": "Modifier les étiquettes de statut", "editTags": "Modifier les balises", "evidenceData": "<PERSON><PERSON><PERSON>", "evidenceType": "Type de preuve", "evidenceTypesWithColon": "Types de preuve :", "fileOwner": "Proprié<PERSON> fi<PERSON>er", "fileStatus": "Statut du fichier", "fileUploader": "Téléverseur du fichier", "goToNewCase": "Aller au nouveau cas", "setVisibility": "Définir la visibilité", "setColor": "Définir la couleur", "inactive": "Inactif", "keyword": "Mot-clé", "move": "<PERSON><PERSON><PERSON><PERSON>", "nameStatusAndAssignColor": "Nommez votre statut et attribuez une couleur.", "nameTagAndChooseVisibility": "Ajou<PERSON>z jusqu'à dix balises à la fois. Les balises ont un maximum de quinze caractères. Séparez-les par une virgule.", "noCaseSelected": "Aucun cas sélectionné", "noCasesFoundDescription": "Vous n'avez créé aucun cas. Cliquez sur le bouton Ajouter un nouveau cas pour commencer.", "noFilesFound": "<PERSON><PERSON><PERSON> fi<PERSON>er de cas trouvé", "noFilesFoundDescription": "Téléversez des fichiers dans votre cas pour voir les résultats de cognition{br} et partager vos découvertes.", "nothingFound": "Aucun résultat", "overview": "<PERSON><PERSON><PERSON><PERSON>", "permissions": "Autorisations", "pleaseSelectCase": "Choisissez un cas dans le tableau pour voir ses détails ici.", "presetColors": "Couleurs prédéfinies :", "color": "<PERSON><PERSON><PERSON>", "resetAll": "<PERSON><PERSON> réinitialiser", "resetFilters": "Réinitialiser les filtres", "retentionDate": "Date de rétention", "save": "Enregistrer", "saveChanges": "Enregistrer les modifications", "search": "<PERSON><PERSON><PERSON>", "selectAStatus": "Sélectionner un statut", "selectStoredLocation": "Sélectionnez l'emplacement où ce contenu sera stocké.", "emptyState": "État vide", "settings": "Paramètres", "share": "Partager", "shareCase": "Partager le cas", "shares": "Partages", "someThingWentWrong": "Une erreur s'est produite...", "statusName": "Nom du statut", "statuses": "Statuts", "tagName": "Nom(s) de la balise", "tags": "Balises", "unableToLoadData": "Les données n'ont pas été trouvées ou n'ont pas pu être chargées.{br} Essayez d'actualiser cette page ou réessayez plus tard.", "unableToUploadFile": "Impossible de téléverser le fichier", "uploadFileSuccess": "<PERSON><PERSON><PERSON> {name} téléversé avec succès", "refreshPage": "Actualisez la page et réessayez", "summary": "Résumé", "NoResults": "Aucun résultat", "NoEngineRun": "Aucune exécution de moteur", "uploadDate": "Date de téléversement", "uploadFile(s)": "<PERSON><PERSON><PERSON><PERSON><PERSON> le(s) fichier(s)", "uploadFiles": "Téléverser des fichiers", "caseDetail": "<PERSON><PERSON><PERSON> du cas", "viewCaseDetails": "Voir les détails du cas", "viewShareDetails": "Voir les détails du partage", "yesDeleteCase": "<PERSON><PERSON>, supprimer le cas", "youCanStartAddingFiles": "Vous pouvez commencer à ajouter des fichiers et des informations sur le cas.", "selectAFolder": "<PERSON><PERSON><PERSON><PERSON> vers le cas", "chooseCase": "Choisissez un cas actif pour déplacer le(s) fichier(s).", "chooseCaseAddFiles": "Choisissez un cas actif pour ajouter le(s) fichier(s).", "selectACasePlaceholder": "Sélectionner un cas", "casePendingDelete": "Ce cas est en attente de suppression et ne peut pas être sélectionné.", "caseDeletedSuccessfully": "Suppression du cas en cours. Il sera bientôt supprimé.", "caseDeletionFailed": "Échec de la suppression du cas", "caseUpdatedSuccess": "Cas mis à jour avec succès", "caseUpdateFailed": "Échec de la mise à jour du cas", "searchPage": "Rechercher et gérer les fichiers", "fileType": "Type de fichier", "defaultEmpty": "--", "wordsInTranscription": "Mots dans la transcription", "faceDetections": "Détections de visage", "objectDescriptors": "Descripteurs d'objet", "vehicleRecognition": "Reconnaissance de véhicule", "licensePlateRecognition": "Reconnaissance de plaque d'immatriculation", "sceneClassification": "Classification de scène", "textRecognition": "Reconnaissance de texte", "callRecording": "Enregistrement d'appel 911", "arrestReport": "Rapport d'arrestation", "bodyWornCamera": "Caméra corporelle", "bookingPhoto": "Photo d'identité", "citizenSubmittedVideo": "Vidéo soumise par un citoyen", "crimeScenePhoto": "Photo de la scène de crime", "inCarVideo": "Vidéo embarq<PERSON>e", "interviewAudioRecording": "Enregistrement audio d'entretien", "interviewRoomRecording": "Enregistrement de la salle d'entretien", "mobileDeviceExtraction": "Extraction d'appareil mobile", "securityCameraVideo": "Vidéo de caméra de sécurité", "video": "Vidéo", "audio": "Audio", "document": "Document", "image": "Image", "title": "Titre", "noResultsFound": "Aucun résultat trouvé", "refineAndTryAgain": "Affinez votre terme de recherche et réessayez.", "confirmDiscardChanges": "Êtes-vous sûr de vouloir abandonner vos modifications ?", "addStatusConfirm": "Confirmation d'ajout de statut", "addTagConfirm": "Confirmation d'ajout de balise", "wouldYouLikeToSaveYourChanges": "Voulez-vous enregistrer vos modifications ?", "wouldYouLikeToSaveYourChangesBeforeClosing": "Voulez-vous enregistrer vos modifications avant de fermer ?", "selectedTagsWillAllBeSetToThisOption": "Les balises sélectionnées seront toutes définies sur cette option.", "selectedStatusesWillAllBeSetToThisOption": "Les statuts sélectionnés seront tous définis sur cette option.", "setTagVisibility": "Définir la visibilité de la balise", "setStatusVisibility": "Définir la visibilité du statut", "fileName": "Nom du fichier", "filename": "Nom du fichier", "sourceName": "Nom de la source", "caseId": "ID du cas", "dateUploaded": "Date de téléversement", "selectUpToTenTags": "Sélectionnez jusqu'à 10 balises préconfigurées", "tagsSelected": "Balises s<PERSON>", "filePendingDelete": "Ce fichier est en attente de suppression et ne peut pas être sélectionné.", "fileDeletedSuccessfully": "Suppression du fichier en cours. Il sera supprimé sous peu.", "fileDeletionFailed": "Échec de la suppression du fichier.", "yesDeleteFile": "<PERSON><PERSON>, supprimer le fichier", "deleteFileConfirmationMsg": "La suppression d'un fichier ne peut pas être annulée", "viewCase": "<PERSON><PERSON><PERSON><PERSON> le cas", "uploaded": "Téléversé", "uploadedWithColons": "Téléversé :", "viewFile": "<PERSON><PERSON><PERSON><PERSON> le fi<PERSON>er", "addToCase": "Ajouter au cas", "moveToAnotherCase": "<PERSON><PERSON><PERSON><PERSON> vers un autre cas", "selected": "Sélectionné", "file": "<PERSON><PERSON><PERSON>", "files": "Fichiers", "addTagSuccess": "<PERSON>se a<PERSON> avec succès", "addTagFailure": "Échec de l'ajout de la balise", "saveTagsSuccess": "Balises enregistrées avec succès", "saveTagsFailure": "Échec de l'enregistrement des balises", "addStatusSuccess": "Statut ajouté avec succès", "addStatusFailure": "Échec de l'ajout du statut", "saveStatusesSuccess": "Statuts enregistrés avec succès", "saveStatusesFailure": "Échec de l'enregistrement des statuts", "filters": "Filtres", "reset": "Réinitialiser", "status": "Statut", "apply": "Appliquer", "showAll": "<PERSON><PERSON><PERSON><PERSON> tout", "allowedLetters": "<PERSON><PERSON> les lettres, chiffres, _ et - sont autorisés", "saveStatusesConcurrentModificationError": "Les statuts ont été modifiés par un autre utilisateur. Vous devrez peut-être actualiser la page.", "saveTagsConcurrentModificationError": "Les balises ont été modifiées par un autre utilisateur. Vous devrez peut-être actualiser la page.", "saveModifiedStatusesMessage": "Les statuts ont été modifiés par un autre utilisateur. <PERSON><PERSON> p<PERSON> 'Abandonner' vos modifications ou 'Enregistrer', ce qui peut écraser leurs données.", "saveModifiedTagsMessage": "Les balises ont été modifiées par un autre utilisateur. <PERSON><PERSON> 'Abandonner' vos modifications ou 'Enregistrer', ce qui peut écraser leurs données.", "selectACategory": "Sélectionnez une catégorie pour commencer", "dataModified": "Donn<PERSON> modifi<PERSON>", "discardChanges": "Abandonner les modifications", "discard": "<PERSON><PERSON><PERSON><PERSON>", "fileMovedSuccess": "<PERSON><PERSON>er déplacé avec succès", "invalidCharacterErrorMessage": "Caractère invalide, seuls \"-\" et \"_\" sont autorisés.", "statusLabelNameAlreadyTaken": "Le nom de l'étiquette de statut est déjà pris.", "editMetadata": "Modifier les métadonnées", "editMetadataWarning": "Toute modification des métadonnées de l'actif est permanente et ne peut pas être annulée.", "fileSize": "<PERSON><PERSON>", "fileFormat": "Format du fichier", "fileId": "ID du fichier", "duration": "<PERSON><PERSON><PERSON>", "source": "Source", "confirmSaveChanges": "Confirmation des modifications", "fileNameRequired": "Le nom du fichier est requis", "caseNameRequired": "Le nom du cas est requis", "showTotalResults": "Affichage de {total} résultats", "sortCategory": "Catégorie de tri : ", "view": "Vue :", "recentlyUploaded": "<PERSON><PERSON><PERSON><PERSON><PERSON> té<PERSON>", "grouped": "Groupé", "unGrouped": "Non groupé", "showAllCategories": "A<PERSON><PERSON><PERSON> toutes les catégories", "hideIfNoResults": "Masquer s'il n'y a pas de résultats", "transcription": "Transcription", "faceRecognition": "Reconnaissance faciale", "objectDetection": "Détection d'objet", "metadata": "Métadonnées", "filterSearchWarning": "Les recherches par mot-clé peuvent prendre un certain temps à charger.", "no": "Non", "found": "<PERSON><PERSON><PERSON><PERSON>", "reAssignTo": "Réaffecter à :", "noOptionsLeft": "Aucune option restante !", "noDataAvailable": "<PERSON><PERSON><PERSON> donnée disponible", "lightMode": "Mode clair", "darkMode": "Mode sombre", "space": " ", "blurImages": "Flouter les images", "all": "<PERSON>ut", "none": "Aucun", "results": "Résultats", "cadId": "ID CAD", "callerPhoneNumber": "Numéro de téléphone de l'appelant", "reportNumber": "Numéro du rapport", "officerName": "Nom de l'agent", "badgeId": "ID du badge", "deviceId": "ID de l'appareil", "deviceNumber": "Numéro de l'appareil", "cameraPhysicalLocation": "Emplacement physique de la caméra", "cameraPhysicalAddress": "Adresse physique de la caméra", "locationTimeline": "Chronologie de l'emplacement", "lastName": "Nom de famille", "firstName": "Prénom", "dateOfBirth": "Date de naissance", "citizenName": "Nom du citoyen", "cameraType": "Type de caméra", "evidenceTechnician": "Technicien de preuve", "unitNumber": "Numéro d'unité", "interviewer": "Interviewer", "interviewee": "Interviewé", "interviewRoom": "<PERSON> d'en<PERSON>tien", "deviceName": "Nom de l'appareil", "deviceType": "Type d'appareil", "deviceModel": "<PERSON><PERSON><PERSON><PERSON> d'appareil", "deviceRegisteredOwner": "Propriétaire enregistré de l'appareil", "cameraFacingDirection": "Orientation de la caméra", "metadataUpdated": "Métadonnées mises à jour", "assetStatus": "Statut de l'actif", "createdBy": "C<PERSON><PERSON> par", "contentType": "Type de contenu", "contentTypesWithColon": "Types de contenu :", "failedToUpdateMetadata": "Échec de la mise à jour des métadonnées", "case-evidence-list-item": "{type} ({count})", "foundString": "Résultats", "foundFullTextString": "Texte brut", "libraries": "Bibliothèques", "entities": "Entités", "getLibrariesFailed": "Échec de l'obtention des bibliothèques", "getEntitiesFailed": "Échec de l'obtention des entités", "enteredMoreThanTenTags": "Vous avez saisi plus de 10 balises. Veuillez en saisir jusqu'à 10.", "invalidCharacterInTag": "Caractère invalide dans {tag}. <PERSON><PERSON> les lettres, chiffres, _ et - sont autorisés.", "tagNameIsTooLong": "La balise {tag} est trop longue. Veuillez saisir une balise de 15 caractères maximum.", "tagNameAlreadyTaken": "Les balises suivantes existent déjà : {tags}", "startExploring": "Commencer à explorer", "searchForAnything": "Recherchez n'importe quoi. Nous vous aiderons à le trouver.", "clickTheCategoryWheel": "Cliquez sur la roue des catégories pour choisir les catégories de recherche.", "invalidCase": "Le cas a été supprimé ou n'existe pas. Veuillez sélectionner un autre cas.", "noneOperatorWarning": "Veuillez sélectionner un opérateur avant d'en sélectionner un nouveau", "somethingWrongSearch": "Une erreur s'est produite lors de votre recherche. ({gqlRequestId})", "metadataCouldNotBeFound": "Les métadonnées n'ont pas pu être trouvées", "failedToRetrieveMetadata": "Échec de la récupération des métadonnées", "noRootFolder": "Aucun cas racine n'est créé. Veuillez contacter votre administrateur.", "youAreAboutDeleteCase": "Vous êtes sur le point de supprimer le cas sélectionné.", "yesDeleteFiles": "<PERSON><PERSON>, supprimer le(s) fichier(s) sélectionné(s)", "shareName": "Nom du partage", "message": "Message", "expirationDate": "Date d'expiration", "recipients": "<PERSON><PERSON><PERSON><PERSON>", "addRecipient": "Ajouter un destinataire", "youAreAboutToShareSensitiveContent": "Vous êtes sur le point de partager du contenu et des fichiers sensibles avec ces destinataires", "emailAddresses": "Adresses e-mail", "createShare": "<PERSON><PERSON><PERSON>", "createShareSubtitle": "Sélectionnez les fichiers auxquels vous souhaitez que les destinataires aient accès et choisissez quand le partage expire.", "shareNameRequired": "Le nom de partage est obligatoire", "messageRequired": "Le message est obligatoire", "expirationDateRequired": "La date d'expiration est requise", "emailRequired": "L'e-mail est obligatoire", "firstNameRequired": "Le prénom est obligatoire", "lastNameRequired": "Apellido es requerido", "expirationDateNotGreaterThanNow": "La date d'expiration doit être supérieure à la date actuelle", "emailBadFormat": "L'e-mail n'est pas un format valide", "removeOperator": "Supprimer l’opérateur", "invalidExpression": "Expression invalide", "lackOfOpenParenthesis": "Parenthèse ouvrante manquante", "lackOfCloseParenthesis": "Parenthèse fermante manquante", "redundantParenthesis": "Parenthèses redondantes", "loading": "Chargement...", "cognitionFiltersNotAvailable": "Face and object search requires cognition libraries to exist in the organization.", "failedGetCaseSchemaId": "Failed to get case schema ID", "failedGetStatusSchemaId": "Failed to get status schema ID", "failedGetTagSchemaId": "Failed to get tag schema ID", "failedGetEvidenceTypeSchemaId": "Failed to get evidenceType schema ID", "noStatus": "Aucun statut", "caseTags": "Étiquettes de cas", "noCasesPleaseCreateACase": "Aucun cas trouvé. Veuillez créer un cas pour commencer.", "caseTagSettings": "Paramètres d'étiquettes de cas", "dateCreated": "Date de création", "password": "<PERSON><PERSON> de passe (Facultative)", "showPassword": "Afficher le mot de passe", "rowPerPage": "Lignes par page", "visibility": "Visibilité", "caseCreateDescriptionCharCount": "{length}/1000", "shareManager": "Gestion du partage", "sharedBy": "Partagé par :", "fileCount": "# de fichiers", "recipientCount": "# de destinataires", "expDate": "Date d'exp.", "deleteShareConfirmationMsg": "La suppression d'un partage et de tous ses fichiers ne peut pas être annulée.", "yesDeleteShare": "<PERSON><PERSON>, supprimer le partage", "access": "Accès", "userGrantedAccess": "L'utilisateur a accordé l'accès à ce partage", "addUser": "Ajouter un utilisateur", "additionalInformation": "Informations supplémentaires", "expirationDateWithColons": "Date d'expiration:", "sharedDateWithColons": "Date partagée:", "numberOfFiles": "Nombre de fichiers", "emailAddress": "Adresse e-mail", "sendButton": "Envoyer", "resendShare": "Ren<PERSON><PERSON> le partage", "removeUser": "Supprimer l'utilisateur", "changeShareExpirationDate": "Modifier la date d'expiration du partage", "fileNotInCase": "Le fichier n'est pas actuellement dans un cas. Veuillez déplacer ce dossier dans un cas."}