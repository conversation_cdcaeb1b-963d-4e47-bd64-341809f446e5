import { GQLApi } from '@utils/helpers';

export async function getCase(
  { folderId, caseRegistryId }: { folderId: string; caseRegistryId: string },
  gql: GQLApi
) {
  const caseFolder = await gql.getFolder({ folderId });
  if (!caseFolder) {
    throw new Error('no case folder');
  }
  if (!caseFolder.contentTemplates?.[0]?.sdo) {
    throw new Error('no case folder contentTemplates');
  }

  const schemaIds = await gql.getAllSchemaIds(caseRegistryId);
  const caseTemplate = caseFolder.contentTemplates.find((template) =>
    schemaIds.includes(template.sdo.schemaId)
  );

  if (!caseTemplate) {
    throw new Error('No valid case content template found');
  }

  return {
    ...caseTemplate.sdo.data,
    sdoId: caseTemplate.sdo.id,
  };
}
