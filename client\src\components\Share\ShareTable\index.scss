.share-table-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  .share-table-cell {
    position: relative;
    width: 100%;
    &::before {
      content: '&nbsp;';
      visibility: hidden;
    }
    & > span {
      position: absolute;
      left: 0;
      right: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .Sdk-MuiTableCell-root:first-child,
  .table-cell:first-child {
    padding-left: 22px;
  }

  .menu-cell {
    padding-right: 16px !important;
  }
}
